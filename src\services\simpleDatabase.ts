// Simple in-memory database service for demo purposes
// This provides the same interface as the SQLite service but uses local state

export interface User {
  id: number;
  name: string;
  email: string;
  password: string;
  department: string;
  employeeId: string;
  role: 'teacher' | 'admin';
  createdAt: string;
}

export interface Class {
  id: number;
  name: string;
  code: string;
  schedule: string;
  room: string;
  teacherId: number;
  status: 'active' | 'inactive';
  createdAt: string;
}

export interface Student {
  id: number;
  name: string;
  studentId: string;
  email: string;
  course: string;
  year: string;
  status: 'active' | 'inactive';
  createdAt: string;
}

export interface AttendanceRecord {
  id: number;
  classId: number;
  studentId: number;
  date: string;
  status: 'present' | 'absent' | 'late';
  confidence?: number;
  method: 'manual' | 'facial_recognition';
  createdAt: string;
}

class SimpleDatabaseService {
  private users: User[] = [];
  private classes: Class[] = [];
  private students: Student[] = [];
  private attendanceRecords: AttendanceRecord[] = [];
  private isInitialized = false;

  async init(): Promise<void> {
    if (this.isInitialized) return;
    
    try {
      await this.populateInitialData();
      this.isInitialized = true;
      console.log('Simple database initialized successfully');
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  private async populateInitialData(): Promise<void> {
    // Demo users
    this.users = [
      {
        id: 1,
        name: 'Dr. John Smith',
        email: '<EMAIL>',
        password: 'demo123',
        department: 'Computer Science',
        employeeId: 'EMP2024001',
        role: 'teacher',
        createdAt: new Date().toISOString(),
      },
      {
        id: 2,
        name: 'Prof. Maria Garcia',
        email: '<EMAIL>',
        password: 'password123',
        department: 'Computer Science',
        employeeId: 'EMP2024002',
        role: 'teacher',
        createdAt: new Date().toISOString(),
      },
      {
        id: 3,
        name: 'Admin User',
        email: '<EMAIL>',
        password: 'admin123',
        department: 'Administration',
        employeeId: 'EMP2024003',
        role: 'admin',
        createdAt: new Date().toISOString(),
      },
    ];

    // Demo classes
    this.classes = [
      {
        id: 1,
        name: 'Computer Science 101',
        code: 'CS101',
        schedule: 'MWF 8:00-9:00 AM',
        room: 'Room 201',
        teacherId: 1,
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 2,
        name: 'Data Structures',
        code: 'CS201',
        schedule: 'TTH 10:00-11:30 AM',
        room: 'Room 203',
        teacherId: 1,
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 3,
        name: 'Database Systems',
        code: 'CS301',
        schedule: 'MWF 2:00-3:00 PM',
        room: 'Room 205',
        teacherId: 2,
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 4,
        name: 'Web Development',
        code: 'CS401',
        schedule: 'TTH 1:00-2:30 PM',
        room: 'Room 207',
        teacherId: 2,
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 5,
        name: 'Software Engineering',
        code: 'CS501',
        schedule: 'MWF 10:00-11:00 AM',
        room: 'Room 209',
        teacherId: 1,
        status: 'inactive',
        createdAt: new Date().toISOString(),
      },
    ];

    // Demo students
    this.students = [
      {
        id: 1,
        name: 'Alice Johnson',
        studentId: 'CS2024001',
        email: '<EMAIL>',
        course: 'Computer Science',
        year: '3rd Year',
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 2,
        name: 'Bob Smith',
        studentId: 'CS2024002',
        email: '<EMAIL>',
        course: 'Computer Science',
        year: '2nd Year',
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 3,
        name: 'Carol Davis',
        studentId: 'CS2024003',
        email: '<EMAIL>',
        course: 'Information Technology',
        year: '4th Year',
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 4,
        name: 'David Brown',
        studentId: 'CS2024004',
        email: '<EMAIL>',
        course: 'Computer Science',
        year: '1st Year',
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 5,
        name: 'Eva Wilson',
        studentId: 'CS2024005',
        email: '<EMAIL>',
        course: 'Computer Science',
        year: '3rd Year',
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 6,
        name: 'Frank Miller',
        studentId: 'CS2024006',
        email: '<EMAIL>',
        course: 'Information Technology',
        year: '2nd Year',
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 7,
        name: 'Grace Lee',
        studentId: 'CS2024007',
        email: '<EMAIL>',
        course: 'Computer Science',
        year: '4th Year',
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 8,
        name: 'Henry Taylor',
        studentId: 'CS2024008',
        email: '<EMAIL>',
        course: 'Computer Science',
        year: '1st Year',
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 9,
        name: 'Ivy Chen',
        studentId: 'CS2024009',
        email: '<EMAIL>',
        course: 'Information Technology',
        year: '3rd Year',
        status: 'active',
        createdAt: new Date().toISOString(),
      },
      {
        id: 10,
        name: 'Jack Anderson',
        studentId: 'CS2024010',
        email: '<EMAIL>',
        course: 'Computer Science',
        year: '2nd Year',
        status: 'active',
        createdAt: new Date().toISOString(),
      },
    ];

    // Generate sample attendance records for the past week
    const today = new Date();
    const dates = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      dates.push(date.toISOString().split('T')[0]);
    }

    let recordId = 1;
    this.attendanceRecords = [];

    for (const date of dates) {
      // CS101 attendance (students 1, 2, 4, 8, 10)
      const cs101Students = [1, 2, 4, 8, 10];
      for (const studentId of cs101Students) {
        const statuses = ['present', 'present', 'present', 'absent', 'late'];
        const status = statuses[Math.floor(Math.random() * statuses.length)] as 'present' | 'absent' | 'late';
        
        this.attendanceRecords.push({
          id: recordId++,
          classId: 1,
          studentId,
          date,
          status,
          confidence: status === 'present' || status === 'late' ? 85 + Math.floor(Math.random() * 15) : undefined,
          method: status === 'absent' ? 'manual' : 'facial_recognition',
          createdAt: new Date().toISOString(),
        });
      }

      // CS201 attendance (students 1, 3, 5, 7, 9)
      const cs201Students = [1, 3, 5, 7, 9];
      for (const studentId of cs201Students) {
        const statuses = ['present', 'present', 'present', 'absent', 'late'];
        const status = statuses[Math.floor(Math.random() * statuses.length)] as 'present' | 'absent' | 'late';
        
        this.attendanceRecords.push({
          id: recordId++,
          classId: 2,
          studentId,
          date,
          status,
          confidence: status === 'present' || status === 'late' ? 85 + Math.floor(Math.random() * 15) : undefined,
          method: status === 'absent' ? 'manual' : 'facial_recognition',
          createdAt: new Date().toISOString(),
        });
      }
    }

    console.log('Initial data populated successfully');
  }

  // User operations
  async authenticateUser(email: string, password: string): Promise<User | null> {
    const user = this.users.find(u => u.email === email && u.password === password);
    return user || null;
  }

  async getUserById(id: number): Promise<User | null> {
    const user = this.users.find(u => u.id === id);
    return user || null;
  }

  // Class operations
  async getAllClasses(): Promise<Class[]> {
    return [...this.classes].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  async getClassById(id: number): Promise<Class | null> {
    const classData = this.classes.find(c => c.id === id);
    return classData || null;
  }

  async createClass(classData: Omit<Class, 'id' | 'createdAt'>): Promise<number> {
    const newId = Math.max(...this.classes.map(c => c.id), 0) + 1;
    const newClass: Class = {
      ...classData,
      id: newId,
      createdAt: new Date().toISOString(),
    };
    this.classes.push(newClass);
    return newId;
  }

  async updateClass(id: number, classData: Partial<Omit<Class, 'id' | 'createdAt'>>): Promise<void> {
    const index = this.classes.findIndex(c => c.id === id);
    if (index !== -1) {
      this.classes[index] = { ...this.classes[index], ...classData };
    }
  }

  async deleteClass(id: number): Promise<void> {
    this.classes = this.classes.filter(c => c.id !== id);
  }

  // Student operations
  async getAllStudents(): Promise<Student[]> {
    return [...this.students].sort((a, b) => a.name.localeCompare(b.name));
  }

  async getStudentById(id: number): Promise<Student | null> {
    const student = this.students.find(s => s.id === id);
    return student || null;
  }

  async createStudent(studentData: Omit<Student, 'id' | 'createdAt'>): Promise<number> {
    const newId = Math.max(...this.students.map(s => s.id), 0) + 1;
    const newStudent: Student = {
      ...studentData,
      id: newId,
      createdAt: new Date().toISOString(),
    };
    this.students.push(newStudent);
    return newId;
  }

  async updateStudent(id: number, studentData: Partial<Omit<Student, 'id' | 'createdAt'>>): Promise<void> {
    const index = this.students.findIndex(s => s.id === id);
    if (index !== -1) {
      this.students[index] = { ...this.students[index], ...studentData };
    }
  }

  async deleteStudent(id: number): Promise<void> {
    this.students = this.students.filter(s => s.id !== id);
  }

  async getStudentsByClass(classId: number): Promise<Student[]> {
    // For demo purposes, return a subset of students for each class
    const classStudentMap: { [key: number]: number[] } = {
      1: [1, 2, 4, 8, 10], // CS101
      2: [1, 3, 5, 7, 9],  // CS201
      3: [2, 3, 6, 7, 10], // CS301
      4: [1, 4, 5, 8, 9],  // CS401
      5: [3, 5, 6, 7, 9],  // CS501
    };

    const studentIds = classStudentMap[classId] || [];
    return this.students.filter(s => studentIds.includes(s.id));
  }

  // Attendance operations
  async recordAttendance(attendanceData: Omit<AttendanceRecord, 'id' | 'createdAt'>): Promise<number> {
    const newId = Math.max(...this.attendanceRecords.map(a => a.id), 0) + 1;
    const newRecord: AttendanceRecord = {
      ...attendanceData,
      id: newId,
      createdAt: new Date().toISOString(),
    };

    // Remove existing record for same student, class, and date
    this.attendanceRecords = this.attendanceRecords.filter(
      r => !(r.classId === attendanceData.classId && 
             r.studentId === attendanceData.studentId && 
             r.date === attendanceData.date)
    );

    this.attendanceRecords.push(newRecord);
    return newId;
  }

  async getAttendanceByClass(classId: number, date?: string): Promise<AttendanceRecord[]> {
    let records = this.attendanceRecords.filter(r => r.classId === classId);
    
    if (date) {
      records = records.filter(r => r.date === date);
    }
    
    return records.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  async getAttendanceByStudent(studentId: number, startDate?: string, endDate?: string): Promise<AttendanceRecord[]> {
    let records = this.attendanceRecords.filter(r => r.studentId === studentId);
    
    if (startDate) {
      records = records.filter(r => r.date >= startDate);
    }
    
    if (endDate) {
      records = records.filter(r => r.date <= endDate);
    }
    
    return records.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }

  async getAttendanceStats(classId?: number, startDate?: string, endDate?: string): Promise<any> {
    let records = this.attendanceRecords;
    
    if (classId) {
      records = records.filter(r => r.classId === classId);
    }
    
    if (startDate) {
      records = records.filter(r => r.date >= startDate);
    }
    
    if (endDate) {
      records = records.filter(r => r.date <= endDate);
    }

    const totalRecords = records.length;
    const presentCount = records.filter(r => r.status === 'present').length;
    const absentCount = records.filter(r => r.status === 'absent').length;
    const lateCount = records.filter(r => r.status === 'late').length;
    const attendanceRate = totalRecords > 0 ? Math.round((presentCount / totalRecords) * 100 * 100) / 100 : 0;

    return {
      totalRecords,
      presentCount,
      absentCount,
      lateCount,
      attendanceRate,
    };
  }

  // Enrollment operations (simplified for demo)
  async enrollStudentInClass(classId: number, studentId: number): Promise<void> {
    // In a real implementation, this would manage the class_students table
    console.log(`Enrolled student ${studentId} in class ${classId}`);
  }

  async unenrollStudentFromClass(classId: number, studentId: number): Promise<void> {
    // In a real implementation, this would manage the class_students table
    console.log(`Unenrolled student ${studentId} from class ${classId}`);
  }

  // Utility methods
  async clearAllData(): Promise<void> {
    this.users = [];
    this.classes = [];
    this.students = [];
    this.attendanceRecords = [];
  }

  async closeDatabase(): Promise<void> {
    // No-op for in-memory database
  }
}

export const databaseService = new SimpleDatabaseService();
