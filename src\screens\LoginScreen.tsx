import React, { useState } from 'react';
import { View, StyleSheet, Alert, KeyboardAvoidingView, Platform } from 'react-native';
import { Text, TextInput, Button, Card, useTheme, Snackbar } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useDatabaseContext } from '../contexts/DatabaseContext';

export default function LoginScreen() {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('demo123');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const theme = useTheme();
  const { login, isInitialized } = useDatabaseContext();

  const showSnackbar = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  const validateEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleLogin = async () => {
    if (!email || !password) {
      showSnackbar('Please enter both email and password');
      return;
    }

    if (!validateEmail(email)) {
      showSnackbar('Please enter a valid email address');
      return;
    }

    if (!isInitialized) {
      showSnackbar('Database is still initializing. Please wait...');
      return;
    }

    setIsLoading(true);

    try {
      const success = await login(email, password);

      if (success) {
        await AsyncStorage.setItem('isAuthenticated', 'true');
        showSnackbar('Login successful! Welcome back.');

        // Small delay to show success message
        setTimeout(() => {
          setIsLoading(false);
        }, 500);
      } else {
        setIsLoading(false);
        showSnackbar('Invalid credentials. Please check your email and password.');
      }
    } catch (error) {
      setIsLoading(false);
      showSnackbar('Login failed. Please try again.');
      console.error('Login error:', error);
    }
  };

  const handleDemoLogin = async () => {
    setEmail('<EMAIL>');
    setPassword('demo123');

    setTimeout(() => {
      handleLogin();
    }, 100);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.primary }]}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <View style={styles.content}>
          {/* Logo Section */}
          <View style={styles.logoContainer}>
            <View style={[styles.logoCircle, { backgroundColor: theme.colors.secondary }]}>
              <MaterialCommunityIcons
                name="school"
                size={50}
                color={theme.colors.primary}
              />
            </View>
            <Text variant="headlineMedium" style={[styles.appTitle, { color: theme.colors.secondary }]}>
              AttendWise
            </Text>
            <Text variant="bodyLarge" style={[styles.subtitle, { color: theme.colors.onPrimary }]}>
              Smart Classroom Management
            </Text>
          </View>

          {/* Login Form */}
          <Card style={styles.loginCard}>
            <Card.Content style={styles.cardContent}>
              <Text variant="titleLarge" style={styles.loginTitle}>
                Welcome Back
              </Text>
              <Text variant="bodyMedium" style={styles.loginSubtitle}>
                Sign in to manage your classes
              </Text>

              <View style={styles.inputContainer}>
                <TextInput
                  label="Email Address"
                  value={email}
                  onChangeText={setEmail}
                  mode="outlined"
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoComplete="email"
                  left={<TextInput.Icon icon="email" />}
                  style={styles.input}
                  error={email.length > 0 && !validateEmail(email)}
                />

                <TextInput
                  label="Password"
                  value={password}
                  onChangeText={setPassword}
                  mode="outlined"
                  secureTextEntry={!showPassword}
                  autoComplete="password"
                  left={<TextInput.Icon icon="lock" />}
                  right={
                    <TextInput.Icon
                      icon={showPassword ? "eye-off" : "eye"}
                      onPress={() => setShowPassword(!showPassword)}
                    />
                  }
                  style={styles.input}
                  error={password.length > 0 && password.length < 6}
                />

                <Button
                  mode="contained"
                  onPress={handleLogin}
                  loading={isLoading}
                  disabled={isLoading}
                  style={[styles.loginButton, { backgroundColor: theme.colors.primary }]}
                  contentStyle={styles.buttonContent}
                >
                  {isLoading ? 'Signing in...' : 'Sign In'}
                </Button>

                <Button
                  mode="outlined"
                  onPress={handleDemoLogin}
                  disabled={isLoading}
                  style={styles.demoButton}
                  contentStyle={styles.buttonContent}
                >
                  Try Demo Login
                </Button>
              </View>

              <View style={styles.footer}>
                <Text variant="bodySmall" style={styles.footerText}>
                  Demo: <EMAIL>
                </Text>
                <Text variant="bodySmall" style={styles.footerText}>
                  Password: demo123
                </Text>
              </View>
            </Card.Content>
          </Card>
        </View>
      </KeyboardAvoidingView>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
        style={styles.snackbar}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  appTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    opacity: 0.8,
    textAlign: 'center',
  },
  loginCard: {
    elevation: 8,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  cardContent: {
    padding: 24,
  },
  loginTitle: {
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: 8,
  },
  loginSubtitle: {
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 32,
  },
  inputContainer: {
    gap: 16,
  },
  input: {
    backgroundColor: 'transparent',
  },
  loginButton: {
    marginTop: 8,
    borderRadius: 8,
  },
  demoButton: {
    borderRadius: 8,
    marginTop: 8,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  footer: {
    marginTop: 24,
    alignItems: 'center',
    gap: 4,
  },
  footerText: {
    opacity: 0.6,
    textAlign: 'center',
  },
  snackbar: {
    marginBottom: 20,
  },
});
