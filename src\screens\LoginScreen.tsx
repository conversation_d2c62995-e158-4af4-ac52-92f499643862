import React, { useState } from 'react';
import { View, StyleSheet, Image } from 'react-native';
import { Text, TextInput, Button, Card, useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialCommunityIcons } from '@expo/vector-icons';

export default function LoginScreen() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const theme = useTheme();

  const handleLogin = async () => {
    if (!email || !password) {
      alert('Please enter both email and password');
      return;
    }

    setIsLoading(true);
    
    // Simulate login - replace with actual authentication
    setTimeout(async () => {
      try {
        await AsyncStorage.setItem('isAuthenticated', 'true');
        await AsyncStorage.setItem('teacherName', 'Dr. <PERSON>');
        // Navigation will be handled by App.tsx state change
        // For React Native, we need to trigger a re-render instead of reload
        setIsLoading(false);
        // The app will automatically navigate due to auth state change
      } catch (error) {
        alert('Login failed. Please try again.');
      }
      setIsLoading(false);
    }, 1000);
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.primary }]}>
      <View style={styles.content}>
        {/* Logo Section */}
        <View style={styles.logoContainer}>
          <View style={[styles.logoCircle, { backgroundColor: theme.colors.secondary }]}>
            <MaterialCommunityIcons 
              name="school" 
              size={40} 
              color={theme.colors.primary} 
            />
          </View>
          <Text variant="headlineMedium" style={[styles.appTitle, { color: theme.colors.secondary }]}>
            Smart Attendance
          </Text>
          <Text variant="bodyLarge" style={[styles.subtitle, { color: theme.colors.onPrimary }]}>
            AI-powered classroom management
          </Text>
        </View>

        {/* Login Form */}
        <Card style={styles.loginCard}>
          <Card.Content style={styles.cardContent}>
            <Text variant="titleLarge" style={styles.loginTitle}>
              Welcome Back
            </Text>
            <Text variant="bodyMedium" style={styles.loginSubtitle}>
              Sign in to manage your classes
            </Text>

            <View style={styles.inputContainer}>
              <TextInput
                label="Email Address"
                value={email}
                onChangeText={setEmail}
                mode="outlined"
                keyboardType="email-address"
                autoCapitalize="none"
                left={<TextInput.Icon icon="email" />}
                style={styles.input}
              />

              <TextInput
                label="Password"
                value={password}
                onChangeText={setPassword}
                mode="outlined"
                secureTextEntry={!showPassword}
                left={<TextInput.Icon icon="lock" />}
                right={
                  <TextInput.Icon 
                    icon={showPassword ? "eye-off" : "eye"} 
                    onPress={() => setShowPassword(!showPassword)}
                  />
                }
                style={styles.input}
              />

              <Button
                mode="contained"
                onPress={handleLogin}
                loading={isLoading}
                disabled={isLoading}
                style={[styles.loginButton, { backgroundColor: theme.colors.primary }]}
                contentStyle={styles.buttonContent}
              >
                {isLoading ? 'Signing in...' : 'Sign In'}
              </Button>
            </View>

            <View style={styles.footer}>
              <Text variant="bodySmall" style={styles.footerText}>
                Demo credentials: Any email and password
              </Text>
            </View>
          </Card.Content>
        </Card>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
    justifyContent: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoCircle: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  appTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    opacity: 0.8,
  },
  loginCard: {
    elevation: 8,
    borderRadius: 16,
  },
  cardContent: {
    padding: 24,
  },
  loginTitle: {
    textAlign: 'center',
    fontWeight: 'bold',
    marginBottom: 8,
  },
  loginSubtitle: {
    textAlign: 'center',
    opacity: 0.7,
    marginBottom: 32,
  },
  inputContainer: {
    gap: 16,
  },
  input: {
    backgroundColor: 'transparent',
  },
  loginButton: {
    marginTop: 8,
    borderRadius: 8,
  },
  buttonContent: {
    paddingVertical: 8,
  },
  footer: {
    marginTop: 24,
    alignItems: 'center',
  },
  footerText: {
    opacity: 0.6,
    textAlign: 'center',
  },
});
