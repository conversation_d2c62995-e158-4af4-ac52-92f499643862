
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { 
  GraduationCap, 
  LogOut, 
  Home, 
  Users, 
  BookOpen, 
  BarChart3, 
  Camera 
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';

const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const teacherName = localStorage.getItem('teacherName') || 'Teacher';

  const handleLogout = () => {
    localStorage.removeItem('isAuthenticated');
    localStorage.removeItem('teacherName');
    toast({
      title: "Logged out successfully",
      description: "You have been signed out of the system.",
    });
    navigate('/login');
  };

  const navItems = [
    { path: '/dashboard', label: 'Dashboard', icon: Home },
    { path: '/classes', label: 'Classes', icon: BookOpen },
    { path: '/students', label: 'Students', icon: Users },
    { path: '/attendance', label: 'Attendance', icon: Camera },
    { path: '/reports', label: 'Reports', icon: BarChart3 },
  ];

  const isActive = (path: string) => location.pathname === path;

  return (
    <nav className="bg-ustp-navy shadow-sm border-b border-primary/20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <div className="flex-shrink-0 flex items-center">
              <div className="w-8 h-8 bg-ustp-gold rounded-lg flex items-center justify-center mr-3">
                <GraduationCap className="w-5 h-5 text-ustp-navy" />
              </div>
              <span className="text-xl font-bold text-ustp-gold">Smart Attendance</span>
            </div>
          </div>

          {/* Navigation Links */}
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              {navItems.map((item) => (
                <button
                  key={item.path}
                  onClick={() => navigate(item.path)}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors flex items-center space-x-2 ${
                    isActive(item.path)
                      ? 'bg-ustp-gold text-ustp-navy'
                      : 'text-ustp-gold/80 hover:text-ustp-gold hover:bg-ustp-gold/10'
                  }`}
                >
                  <item.icon className="w-4 h-4" />
                  <span>{item.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* User Menu */}
          <div className="flex items-center space-x-4">
            <div className="hidden md:block text-right">
              <p className="text-sm font-medium text-ustp-gold">{teacherName}</p>
              <p className="text-xs text-ustp-gold/70">Teacher</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleLogout}
              className="flex items-center space-x-2 border-ustp-gold text-ustp-gold hover:bg-ustp-gold hover:text-ustp-navy"
            >
              <LogOut className="w-4 h-4" />
              <span className="hidden sm:inline">Logout</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden">
        <div className="px-2 pt-2 pb-3 space-y-1 bg-ustp-navy/90">
          {navItems.map((item) => (
            <button
              key={item.path}
              onClick={() => navigate(item.path)}
              className={`w-full text-left px-3 py-2 rounded-md text-base font-medium transition-colors flex items-center space-x-2 ${
                isActive(item.path)
                  ? 'bg-ustp-gold text-ustp-navy'
                  : 'text-ustp-gold/80 hover:text-ustp-gold hover:bg-ustp-gold/10'
              }`}
            >
              <item.icon className="w-4 h-4" />
              <span>{item.label}</span>
            </button>
          ))}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;
