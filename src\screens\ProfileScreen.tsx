import React from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { Text, Avatar, List, Switch, Button, useTheme, Card } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MaterialCommunityIcons } from '@expo/vector-icons';

export default function ProfileScreen() {
  const theme = useTheme();
  const [notificationsEnabled, setNotificationsEnabled] = React.useState(true);
  const [darkModeEnabled, setDarkModeEnabled] = React.useState(false);

  const handleLogout = () => {
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            try {
              await AsyncStorage.removeItem('isAuthenticated');
              await AsyncStorage.removeItem('teacherName');
              // The app will automatically navigate due to auth state change
              Alert.alert('Success', 'You have been signed out successfully.');
            } catch (error) {
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            }
          }
        }
      ]
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <Card style={styles.headerCard}>
          <Card.Content style={styles.headerContent}>
            <Avatar.Text
              size={80}
              label="SJ"
              style={[styles.avatar, { backgroundColor: theme.colors.primary }]}
              labelStyle={{ color: theme.colors.secondary }}
            />
            <Text variant="headlineMedium" style={styles.name}>Dr. Sarah Johnson</Text>
            <Text variant="bodyLarge" style={styles.role}>Teacher</Text>
            <Text variant="bodyMedium" style={styles.email}><EMAIL></Text>
          </Card.Content>
        </Card>

        {/* Settings Section */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.sectionTitle}>Settings</Text>

            <View style={styles.settingsContainer}>
              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <MaterialCommunityIcons
                    name="bell"
                    size={24}
                    color={theme.colors.primary}
                  />
                  <Text variant="bodyLarge" style={styles.settingLabel}>
                    Notifications
                  </Text>
                </View>
                <Switch
                  value={notificationsEnabled}
                  onValueChange={setNotificationsEnabled}
                />
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <MaterialCommunityIcons
                    name="theme-light-dark"
                    size={24}
                    color={theme.colors.primary}
                  />
                  <Text variant="bodyLarge" style={styles.settingLabel}>
                    Dark Mode
                  </Text>
                </View>
                <Switch
                  value={darkModeEnabled}
                  onValueChange={setDarkModeEnabled}
                />
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <MaterialCommunityIcons
                    name="account-edit"
                    size={24}
                    color={theme.colors.primary}
                  />
                  <Text variant="bodyLarge" style={styles.settingLabel}>
                    Edit Profile
                  </Text>
                </View>
                <MaterialCommunityIcons
                  name="chevron-right"
                  size={24}
                  color={theme.colors.onSurfaceVariant}
                />
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <MaterialCommunityIcons
                    name="lock"
                    size={24}
                    color={theme.colors.primary}
                  />
                  <Text variant="bodyLarge" style={styles.settingLabel}>
                    Change Password
                  </Text>
                </View>
                <MaterialCommunityIcons
                  name="chevron-right"
                  size={24}
                  color={theme.colors.onSurfaceVariant}
                />
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* About Section */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.sectionTitle}>About</Text>

            <View style={styles.settingsContainer}>
              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <MaterialCommunityIcons
                    name="information"
                    size={24}
                    color={theme.colors.primary}
                  />
                  <View>
                    <Text variant="bodyLarge" style={styles.settingLabel}>
                      Version
                    </Text>
                    <Text variant="bodyMedium" style={styles.settingDescription}>
                      1.0.0
                    </Text>
                  </View>
                </View>
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <MaterialCommunityIcons
                    name="file-document"
                    size={24}
                    color={theme.colors.primary}
                  />
                  <Text variant="bodyLarge" style={styles.settingLabel}>
                    Terms of Service
                  </Text>
                </View>
                <MaterialCommunityIcons
                  name="chevron-right"
                  size={24}
                  color={theme.colors.onSurfaceVariant}
                />
              </View>

              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <MaterialCommunityIcons
                    name="shield-account"
                    size={24}
                    color={theme.colors.primary}
                  />
                  <Text variant="bodyLarge" style={styles.settingLabel}>
                    Privacy Policy
                  </Text>
                </View>
                <MaterialCommunityIcons
                  name="chevron-right"
                  size={24}
                  color={theme.colors.onSurfaceVariant}
                />
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Sign Out Button */}
        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={handleLogout}
            style={[styles.signOutButton, { backgroundColor: '#ef4444' }]}
            icon="logout"
            contentStyle={styles.buttonContent}
          >
            Sign Out
          </Button>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  headerCard: {
    margin: 20,
    elevation: 2,
  },
  headerContent: {
    alignItems: 'center',
    padding: 24,
  },
  avatar: {
    marginBottom: 16,
  },
  name: {
    fontWeight: 'bold',
    marginBottom: 4,
    textAlign: 'center',
  },
  role: {
    opacity: 0.7,
    marginBottom: 4,
    textAlign: 'center',
  },
  email: {
    opacity: 0.6,
    textAlign: 'center',
  },
  card: {
    margin: 20,
    marginTop: 0,
    elevation: 2,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: 16,
  },
  settingsContainer: {
    gap: 16,
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  settingInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    gap: 16,
  },
  settingLabel: {
    fontWeight: '500',
  },
  settingDescription: {
    opacity: 0.7,
    marginTop: 2,
  },
  buttonContainer: {
    padding: 20,
  },
  signOutButton: {
    borderRadius: 8,
  },
  buttonContent: {
    paddingVertical: 8,
  },
});