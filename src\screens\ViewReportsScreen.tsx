import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Dimensions,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  Chip,
  useTheme,
  SegmentedButtons,
  DataTable,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

interface AttendanceData {
  date: string;
  present: number;
  absent: number;
  total: number;
  percentage: number;
}

interface StudentReport {
  id: string;
  name: string;
  studentId: string;
  present: number;
  absent: number;
  total: number;
  percentage: number;
}

export default function ViewReportsScreen() {
  const theme = useTheme();
  const [reportType, setReportType] = useState('overview');
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  const attendanceData: AttendanceData[] = [
    { date: '2024-01-15', present: 28, absent: 7, total: 35, percentage: 80 },
    { date: '2024-01-16', present: 32, absent: 3, total: 35, percentage: 91.4 },
    { date: '2024-01-17', present: 30, absent: 5, total: 35, percentage: 85.7 },
    { date: '2024-01-18', present: 33, absent: 2, total: 35, percentage: 94.3 },
    { date: '2024-01-19', present: 29, absent: 6, total: 35, percentage: 82.9 },
  ];

  const studentReports: StudentReport[] = [
    { id: '1', name: 'Alice Johnson', studentId: 'CS2024001', present: 18, absent: 2, total: 20, percentage: 90 },
    { id: '2', name: 'Bob Smith', studentId: 'CS2024002', present: 16, absent: 4, total: 20, percentage: 80 },
    { id: '3', name: 'Carol Davis', studentId: 'CS2024003', present: 19, absent: 1, total: 20, percentage: 95 },
    { id: '4', name: 'David Brown', studentId: 'CS2024004', present: 15, absent: 5, total: 20, percentage: 75 },
  ];

  const overallStats = {
    totalClasses: 20,
    averageAttendance: 86.8,
    totalStudents: 35,
    presentToday: 32,
  };

  const getAttendanceColor = (percentage: number) => {
    if (percentage >= 90) return theme.colors.secondary;
    if (percentage >= 75) return '#FFD700';
    return theme.colors.error;
  };

  const renderOverviewReport = () => (
    <View>
      {/* Stats Cards */}
      <View style={styles.statsGrid}>
        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <MaterialCommunityIcons name="school" size={32} color={theme.colors.secondary} />
            <Text variant="headlineSmall" style={[styles.statNumber, { color: theme.colors.secondary }]}>
              {overallStats.totalClasses}
            </Text>
            <Text variant="bodySmall" style={styles.statLabel}>Total Classes</Text>
          </Card.Content>
        </Card>

        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <MaterialCommunityIcons name="chart-line" size={32} color={theme.colors.primary} />
            <Text variant="headlineSmall" style={[styles.statNumber, { color: theme.colors.primary }]}>
              {overallStats.averageAttendance}%
            </Text>
            <Text variant="bodySmall" style={styles.statLabel}>Avg Attendance</Text>
          </Card.Content>
        </Card>

        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <MaterialCommunityIcons name="account-group" size={32} color={theme.colors.secondary} />
            <Text variant="headlineSmall" style={[styles.statNumber, { color: theme.colors.secondary }]}>
              {overallStats.totalStudents}
            </Text>
            <Text variant="bodySmall" style={styles.statLabel}>Total Students</Text>
          </Card.Content>
        </Card>

        <Card style={styles.statCard}>
          <Card.Content style={styles.statContent}>
            <MaterialCommunityIcons name="account-check" size={32} color={theme.colors.primary} />
            <Text variant="headlineSmall" style={[styles.statNumber, { color: theme.colors.primary }]}>
              {overallStats.presentToday}
            </Text>
            <Text variant="bodySmall" style={styles.statLabel}>Present Today</Text>
          </Card.Content>
        </Card>
      </View>

      {/* Recent Attendance */}
      <Card style={styles.chartCard}>
        <Card.Content>
          <Text variant="titleMedium" style={styles.chartTitle}>
            Recent Attendance Trend
          </Text>
          <View style={styles.chartContainer}>
            {attendanceData.map((data, index) => (
              <View key={index} style={styles.barContainer}>
                <View style={styles.bar}>
                  <View 
                    style={[
                      styles.barFill, 
                      { 
                        height: `${data.percentage}%`,
                        backgroundColor: getAttendanceColor(data.percentage)
                      }
                    ]} 
                  />
                </View>
                <Text variant="bodySmall" style={styles.barLabel}>
                  {new Date(data.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                </Text>
                <Text variant="bodySmall" style={styles.barPercentage}>
                  {data.percentage}%
                </Text>
              </View>
            ))}
          </View>
        </Card.Content>
      </Card>
    </View>
  );

  const renderAttendanceReport = () => (
    <Card style={styles.tableCard}>
      <Card.Content>
        <Text variant="titleMedium" style={styles.tableTitle}>
          Daily Attendance Report
        </Text>
        <DataTable>
          <DataTable.Header>
            <DataTable.Title>Date</DataTable.Title>
            <DataTable.Title numeric>Present</DataTable.Title>
            <DataTable.Title numeric>Absent</DataTable.Title>
            <DataTable.Title numeric>Rate</DataTable.Title>
          </DataTable.Header>

          {attendanceData.map((data, index) => (
            <DataTable.Row key={index}>
              <DataTable.Cell>
                {new Date(data.date).toLocaleDateString()}
              </DataTable.Cell>
              <DataTable.Cell numeric>{data.present}</DataTable.Cell>
              <DataTable.Cell numeric>{data.absent}</DataTable.Cell>
              <DataTable.Cell numeric>
                <Chip 
                  style={{ backgroundColor: getAttendanceColor(data.percentage) }}
                  textStyle={{ color: 'white', fontSize: 12 }}
                >
                  {data.percentage}%
                </Chip>
              </DataTable.Cell>
            </DataTable.Row>
          ))}
        </DataTable>
      </Card.Content>
    </Card>
  );

  const renderStudentReport = () => (
    <Card style={styles.tableCard}>
      <Card.Content>
        <Text variant="titleMedium" style={styles.tableTitle}>
          Student Attendance Report
        </Text>
        <DataTable>
          <DataTable.Header>
            <DataTable.Title>Student</DataTable.Title>
            <DataTable.Title numeric>Present</DataTable.Title>
            <DataTable.Title numeric>Absent</DataTable.Title>
            <DataTable.Title numeric>Rate</DataTable.Title>
          </DataTable.Header>

          {studentReports.map((student) => (
            <DataTable.Row key={student.id}>
              <DataTable.Cell>
                <View>
                  <Text variant="bodyMedium">{student.name}</Text>
                  <Text variant="bodySmall" style={{ opacity: 0.6 }}>
                    {student.studentId}
                  </Text>
                </View>
              </DataTable.Cell>
              <DataTable.Cell numeric>{student.present}</DataTable.Cell>
              <DataTable.Cell numeric>{student.absent}</DataTable.Cell>
              <DataTable.Cell numeric>
                <Chip 
                  style={{ backgroundColor: getAttendanceColor(student.percentage) }}
                  textStyle={{ color: 'white', fontSize: 12 }}
                >
                  {student.percentage}%
                </Chip>
              </DataTable.Cell>
            </DataTable.Row>
          ))}
        </DataTable>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.primary }]}>
          View Reports
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Analyze attendance data and trends
        </Text>
      </View>

      <View style={styles.controls}>
        <SegmentedButtons
          value={reportType}
          onValueChange={setReportType}
          buttons={[
            { value: 'overview', label: 'Overview' },
            { value: 'attendance', label: 'Attendance' },
            { value: 'students', label: 'Students' },
          ]}
          style={styles.segmentedButtons}
        />

        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.periodContainer}>
          <Chip
            selected={selectedPeriod === 'week'}
            onPress={() => setSelectedPeriod('week')}
            style={styles.periodChip}
          >
            This Week
          </Chip>
          <Chip
            selected={selectedPeriod === 'month'}
            onPress={() => setSelectedPeriod('month')}
            style={styles.periodChip}
          >
            This Month
          </Chip>
          <Chip
            selected={selectedPeriod === 'semester'}
            onPress={() => setSelectedPeriod('semester')}
            style={styles.periodChip}
          >
            This Semester
          </Chip>
        </ScrollView>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {reportType === 'overview' && renderOverviewReport()}
        {reportType === 'attendance' && renderAttendanceReport()}
        {reportType === 'students' && renderStudentReport()}

        <View style={styles.exportSection}>
          <Text variant="titleMedium" style={styles.exportTitle}>
            Export Reports
          </Text>
          <View style={styles.exportButtons}>
            <Button
              mode="outlined"
              icon="file-pdf-box"
              style={styles.exportButton}
              onPress={() => {}}
            >
              Export PDF
            </Button>
            <Button
              mode="outlined"
              icon="microsoft-excel"
              style={styles.exportButton}
              onPress={() => {}}
            >
              Export Excel
            </Button>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    opacity: 0.7,
  },
  controls: {
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  segmentedButtons: {
    marginBottom: 10,
  },
  periodContainer: {
    flexDirection: 'row',
  },
  periodChip: {
    marginRight: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  statCard: {
    width: (width - 60) / 2,
    marginBottom: 12,
    elevation: 2,
  },
  statContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  statNumber: {
    fontWeight: 'bold',
    marginVertical: 8,
  },
  statLabel: {
    textAlign: 'center',
    opacity: 0.7,
  },
  chartCard: {
    marginBottom: 20,
    elevation: 2,
  },
  chartTitle: {
    fontWeight: 'bold',
    marginBottom: 16,
  },
  chartContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'flex-end',
    height: 150,
    paddingBottom: 20,
  },
  barContainer: {
    alignItems: 'center',
    flex: 1,
  },
  bar: {
    width: 30,
    height: 100,
    backgroundColor: '#f0f0f0',
    borderRadius: 4,
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  barFill: {
    width: '100%',
    borderRadius: 4,
    minHeight: 4,
  },
  barLabel: {
    marginBottom: 4,
    textAlign: 'center',
  },
  barPercentage: {
    fontWeight: 'bold',
    textAlign: 'center',
  },
  tableCard: {
    marginBottom: 20,
    elevation: 2,
  },
  tableTitle: {
    fontWeight: 'bold',
    marginBottom: 16,
  },
  exportSection: {
    marginTop: 20,
    marginBottom: 40,
  },
  exportTitle: {
    fontWeight: 'bold',
    marginBottom: 16,
  },
  exportButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  exportButton: {
    flex: 1,
  },
});
