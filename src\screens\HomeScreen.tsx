import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Card, Button, useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';

type RootStackParamList = {
  Home: undefined;
  Attendance: undefined;
  Profile: undefined;
};

type HomeScreenNavigationProp = NativeStackNavigationProp<RootStackParamList, 'Home'>;

export default function HomeScreen() {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const theme = useTheme();

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <View style={styles.header}>
          <Text variant="headlineMedium" style={styles.welcomeText}>
            Welcome to AttendWise
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            Manage your classroom attendance efficiently
          </Text>
        </View>

        <View style={styles.cardsContainer}>
          <Card style={styles.card} onPress={() => navigation.navigate('Attendance')}>
            <Card.Content>
              <Text variant="titleLarge">Take Attendance</Text>
              <Text variant="bodyMedium">Record attendance for your current class</Text>
            </Card.Content>
            <Card.Actions>
              <Button mode="contained">Start</Button>
            </Card.Actions>
          </Card>

          <Card style={styles.card}>
            <Card.Content>
              <Text variant="titleLarge">View Reports</Text>
              <Text variant="bodyMedium">Check attendance statistics and reports</Text>
            </Card.Content>
            <Card.Actions>
              <Button mode="contained">View</Button>
            </Card.Actions>
          </Card>

          <Card style={styles.card} onPress={() => navigation.navigate('Profile')}>
            <Card.Content>
              <Text variant="titleLarge">Profile</Text>
              <Text variant="bodyMedium">Manage your account settings</Text>
            </Card.Content>
            <Card.Actions>
              <Button mode="contained">Settings</Button>
            </Card.Actions>
          </Card>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  welcomeText: {
    marginBottom: 8,
    fontWeight: 'bold',
  },
  subtitle: {
    color: '#666',
  },
  cardsContainer: {
    padding: 16,
  },
  card: {
    marginBottom: 16,
    elevation: 2,
  },
}); 