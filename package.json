{"name": "attend-wise-classroom", "version": "1.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^6.1.9", "@react-navigation/native-stack": "^6.9.17", "@tanstack/react-query": "^5.56.2", "date-fns": "^3.6.0", "expo": "~50.0.0", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-hook-form": "^7.53.0", "react-native": "0.73.4", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.14.0", "react-native-paper": "^5.12.3", "react-native-reanimated": "~3.6.2", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "react-native-vector-icons": "^10.0.3", "zod": "^3.23.8"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "typescript": "^5.1.3"}, "private": true}