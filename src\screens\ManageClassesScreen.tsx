import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  FAB,
  Searchbar,
  Chip,
  IconButton,
  useTheme,
  Dialog,
  Portal,
  TextInput,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface ClassData {
  id: string;
  name: string;
  code: string;
  schedule: string;
  room: string;
  students: number;
  status: 'active' | 'inactive';
}

export default function ManageClassesScreen() {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [dialogVisible, setDialogVisible] = useState(false);
  const [editingClass, setEditingClass] = useState<ClassData | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    schedule: '',
    room: '',
  });

  const [classes, setClasses] = useState<ClassData[]>([
    {
      id: '1',
      name: 'Computer Science 101',
      code: 'CS101',
      schedule: 'MWF 8:00-9:00 AM',
      room: 'Room 201',
      students: 35,
      status: 'active',
    },
    {
      id: '2',
      name: 'Data Structures',
      code: 'CS201',
      schedule: 'TTH 10:00-11:30 AM',
      room: 'Room 203',
      students: 28,
      status: 'active',
    },
    {
      id: '3',
      name: 'Database Systems',
      code: 'CS301',
      schedule: 'MWF 2:00-3:00 PM',
      room: 'Room 205',
      students: 32,
      status: 'inactive',
    },
  ]);

  const filteredClasses = classes.filter(cls => {
    const matchesSearch = cls.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         cls.code.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterStatus === 'all' || cls.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const handleAddClass = () => {
    setEditingClass(null);
    setFormData({ name: '', code: '', schedule: '', room: '' });
    setDialogVisible(true);
  };

  const handleEditClass = (cls: ClassData) => {
    setEditingClass(cls);
    setFormData({
      name: cls.name,
      code: cls.code,
      schedule: cls.schedule,
      room: cls.room,
    });
    setDialogVisible(true);
  };

  const handleSaveClass = () => {
    if (!formData.name || !formData.code) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    if (editingClass) {
      // Update existing class
      setClasses(prev => prev.map(cls => 
        cls.id === editingClass.id 
          ? { ...cls, ...formData }
          : cls
      ));
    } else {
      // Add new class
      const newClass: ClassData = {
        id: Date.now().toString(),
        ...formData,
        students: 0,
        status: 'active',
      };
      setClasses(prev => [...prev, newClass]);
    }

    setDialogVisible(false);
    setFormData({ name: '', code: '', schedule: '', room: '' });
  };

  const handleDeleteClass = (classId: string) => {
    Alert.alert(
      'Delete Class',
      'Are you sure you want to delete this class?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => setClasses(prev => prev.filter(cls => cls.id !== classId)),
        },
      ]
    );
  };

  const toggleClassStatus = (classId: string) => {
    setClasses(prev => prev.map(cls => 
      cls.id === classId 
        ? { ...cls, status: cls.status === 'active' ? 'inactive' : 'active' }
        : cls
    ));
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.primary }]}>
          Manage Classes
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Create and manage your class schedules
        </Text>
      </View>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search classes..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterContainer}>
          <Chip
            selected={filterStatus === 'all'}
            onPress={() => setFilterStatus('all')}
            style={styles.filterChip}
          >
            All ({classes.length})
          </Chip>
          <Chip
            selected={filterStatus === 'active'}
            onPress={() => setFilterStatus('active')}
            style={styles.filterChip}
          >
            Active ({classes.filter(c => c.status === 'active').length})
          </Chip>
          <Chip
            selected={filterStatus === 'inactive'}
            onPress={() => setFilterStatus('inactive')}
            style={styles.filterChip}
          >
            Inactive ({classes.filter(c => c.status === 'inactive').length})
          </Chip>
        </ScrollView>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {filteredClasses.map((cls) => (
          <Card key={cls.id} style={styles.classCard}>
            <Card.Content>
              <View style={styles.classHeader}>
                <View style={styles.classInfo}>
                  <Text variant="titleMedium" style={styles.className}>
                    {cls.name}
                  </Text>
                  <Text variant="bodyMedium" style={styles.classCode}>
                    {cls.code}
                  </Text>
                </View>
                <View style={styles.classActions}>
                  <Chip
                    mode="outlined"
                    style={[
                      styles.statusChip,
                      { backgroundColor: cls.status === 'active' ? theme.colors.secondary : theme.colors.surface }
                    ]}
                    textStyle={{ color: cls.status === 'active' ? theme.colors.primary : theme.colors.onSurface }}
                  >
                    {cls.status}
                  </Chip>
                </View>
              </View>

              <View style={styles.classDetails}>
                <View style={styles.detailRow}>
                  <MaterialCommunityIcons name="clock-outline" size={16} color={theme.colors.primary} />
                  <Text variant="bodySmall" style={styles.detailText}>
                    {cls.schedule}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <MaterialCommunityIcons name="map-marker-outline" size={16} color={theme.colors.primary} />
                  <Text variant="bodySmall" style={styles.detailText}>
                    {cls.room}
                  </Text>
                </View>
                <View style={styles.detailRow}>
                  <MaterialCommunityIcons name="account-group-outline" size={16} color={theme.colors.primary} />
                  <Text variant="bodySmall" style={styles.detailText}>
                    {cls.students} students
                  </Text>
                </View>
              </View>

              <View style={styles.actionButtons}>
                <Button
                  mode="outlined"
                  onPress={() => handleEditClass(cls)}
                  style={styles.actionButton}
                  icon="pencil"
                >
                  Edit
                </Button>
                <Button
                  mode="outlined"
                  onPress={() => toggleClassStatus(cls.id)}
                  style={styles.actionButton}
                  icon={cls.status === 'active' ? 'pause' : 'play'}
                >
                  {cls.status === 'active' ? 'Deactivate' : 'Activate'}
                </Button>
                <IconButton
                  icon="delete"
                  size={20}
                  onPress={() => handleDeleteClass(cls.id)}
                  iconColor={theme.colors.error}
                />
              </View>
            </Card.Content>
          </Card>
        ))}

        {filteredClasses.length === 0 && (
          <View style={styles.emptyState}>
            <MaterialCommunityIcons name="school-outline" size={64} color={theme.colors.outline} />
            <Text variant="titleMedium" style={styles.emptyTitle}>
              No classes found
            </Text>
            <Text variant="bodyMedium" style={styles.emptySubtitle}>
              {searchQuery ? 'Try adjusting your search' : 'Create your first class to get started'}
            </Text>
          </View>
        )}
      </ScrollView>

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.secondary }]}
        onPress={handleAddClass}
        label="Add Class"
      />

      <Portal>
        <Dialog visible={dialogVisible} onDismiss={() => setDialogVisible(false)}>
          <Dialog.Title>{editingClass ? 'Edit Class' : 'Add New Class'}</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Class Name *"
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              style={styles.input}
              mode="outlined"
            />
            <TextInput
              label="Class Code *"
              value={formData.code}
              onChangeText={(text) => setFormData(prev => ({ ...prev, code: text }))}
              style={styles.input}
              mode="outlined"
            />
            <TextInput
              label="Schedule"
              value={formData.schedule}
              onChangeText={(text) => setFormData(prev => ({ ...prev, schedule: text }))}
              style={styles.input}
              mode="outlined"
              placeholder="e.g., MWF 8:00-9:00 AM"
            />
            <TextInput
              label="Room"
              value={formData.room}
              onChangeText={(text) => setFormData(prev => ({ ...prev, room: text }))}
              style={styles.input}
              mode="outlined"
              placeholder="e.g., Room 201"
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setDialogVisible(false)}>Cancel</Button>
            <Button onPress={handleSaveClass} mode="contained">
              {editingClass ? 'Update' : 'Create'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    opacity: 0.7,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  searchbar: {
    marginBottom: 10,
  },
  filterContainer: {
    flexDirection: 'row',
  },
  filterChip: {
    marginRight: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  classCard: {
    marginBottom: 12,
    elevation: 2,
  },
  classHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  classInfo: {
    flex: 1,
  },
  className: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  classCode: {
    opacity: 0.7,
  },
  classActions: {
    marginLeft: 12,
  },
  statusChip: {
    height: 28,
  },
  classDetails: {
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  detailText: {
    marginLeft: 8,
    opacity: 0.8,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  input: {
    marginBottom: 12,
  },
});
