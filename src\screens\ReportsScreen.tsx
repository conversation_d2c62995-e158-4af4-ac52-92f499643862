import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Dimensions, Alert } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  useTheme,
  SegmentedButtons
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>hart } from 'react-native-chart-kit';

const { width } = Dimensions.get('window');

export default function ReportsScreen() {
  const theme = useTheme();
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  const stats = [
    {
      title: 'Overall Attendance',
      value: '91.2%',
      icon: 'trending-up',
      color: '#3b82f6',
      change: '****% vs last week'
    },
    {
      title: 'Total Students',
      value: '156',
      icon: 'account-group',
      color: '#10b981',
      change: 'Across 4 classes'
    },
    {
      title: 'Classes Today',
      value: '4',
      icon: 'calendar',
      color: '#8b5cf6',
      change: 'All completed'
    },
    {
      title: 'Average Score',
      value: '8.7/10',
      icon: 'chart-bar',
      color: '#f97316',
      change: 'Excellent'
    }
  ];

  const weeklyData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
    datasets: [
      {
        data: [145, 138, 149, 142, 136],
        color: (opacity = 1) => `rgba(30, 41, 59, ${opacity})`,
        strokeWidth: 2
      }
    ]
  };

  const attendanceDistribution = [
    {
      name: 'Excellent\n(90-100%)',
      population: 68,
      color: '#10B981',
      legendFontColor: '#374151',
      legendFontSize: 12,
    },
    {
      name: 'Good\n(80-89%)',
      population: 45,
      color: '#3B82F6',
      legendFontColor: '#374151',
      legendFontSize: 12,
    },
    {
      name: 'Fair\n(70-79%)',
      population: 28,
      color: '#F59E0B',
      legendFontColor: '#374151',
      legendFontSize: 12,
    },
    {
      name: 'Poor\n(<70%)',
      population: 15,
      color: '#EF4444',
      legendFontColor: '#374151',
      legendFontSize: 12,
    },
  ];

  const classData = [
    { name: 'CS 101', attendance: 92, students: 28, trend: '****%' },
    { name: 'Data Structures', attendance: 88, students: 24, trend: '****%' },
    { name: 'Machine Learning', attendance: 95, students: 32, trend: '****%' },
    { name: 'Web Development', attendance: 87, students: 22, trend: '+0.8%' },
  ];

  const chartConfig = {
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(30, 41, 59, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(107, 114, 128, ${opacity})`,
    style: {
      borderRadius: 16
    }
  };

  const handleExport = (format: string) => {
    Alert.alert(
      'Export Report',
      `Exporting report as ${format.toUpperCase()}. Your report will be downloaded shortly.`
    );
  };

  const getStatusColor = (attendance: number) => {
    if (attendance >= 90) return '#10b981';
    if (attendance >= 80) return '#f59e0b';
    return '#ef4444';
  };

  const getStatusText = (attendance: number) => {
    if (attendance >= 90) return 'Excellent';
    if (attendance >= 80) return 'Good';
    return 'Needs Attention';
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text variant="headlineMedium" style={styles.title}>
            Reports & Analytics
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            Comprehensive attendance insights and trends
          </Text>
          
          <View style={styles.exportButtons}>
            <Button 
              mode="outlined" 
              onPress={() => handleExport('pdf')}
              style={styles.exportButton}
              icon="file-pdf-box"
              compact
            >
              PDF
            </Button>
            <Button 
              mode="outlined" 
              onPress={() => handleExport('excel')}
              style={styles.exportButton}
              icon="file-excel"
              compact
            >
              Excel
            </Button>
          </View>
        </View>

        {/* Period Filter */}
        <View style={styles.filterContainer}>
          <SegmentedButtons
            value={selectedPeriod}
            onValueChange={setSelectedPeriod}
            buttons={[
              { value: 'week', label: 'Week' },
              { value: 'month', label: 'Month' },
              { value: 'semester', label: 'Semester' },
            ]}
            style={styles.segmentedButtons}
          />
        </View>

        {/* Summary Cards */}
        <View style={styles.statsGrid}>
          {stats.map((stat, index) => (
            <Card key={index} style={styles.statCard}>
              <Card.Content style={styles.statContent}>
                <View style={styles.statHeader}>
                  <View style={styles.statInfo}>
                    <Text variant="bodySmall" style={styles.statTitle}>{stat.title}</Text>
                    <Text variant="headlineSmall" style={styles.statValue}>{stat.value}</Text>
                    <Text variant="bodySmall" style={styles.statChange}>{stat.change}</Text>
                  </View>
                  <View style={[styles.iconContainer, { backgroundColor: stat.color }]}>
                    <MaterialCommunityIcons 
                      name={stat.icon as any} 
                      size={24} 
                      color="white" 
                    />
                  </View>
                </View>
              </Card.Content>
            </Card>
          ))}
        </View>

        {/* Daily Attendance Trends */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.cardTitle}>
              Daily Attendance Trends
            </Text>
            <Text variant="bodyMedium" style={styles.cardSubtitle}>
              Student attendance patterns throughout the week
            </Text>
            
            <View style={styles.chartContainer}>
              <BarChart
                data={weeklyData}
                width={width - 80}
                height={220}
                chartConfig={chartConfig}
                style={styles.chart}
                showValuesOnTopOfBars
              />
            </View>
          </Card.Content>
        </Card>

        {/* Attendance Distribution */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.cardTitle}>
              Attendance Distribution
            </Text>
            <Text variant="bodyMedium" style={styles.cardSubtitle}>
              Distribution of student attendance rates
            </Text>
            
            <View style={styles.chartContainer}>
              <PieChart
                data={attendanceDistribution}
                width={width - 80}
                height={220}
                chartConfig={chartConfig}
                accessor="population"
                backgroundColor="transparent"
                paddingLeft="15"
                style={styles.chart}
              />
            </View>
          </Card.Content>
        </Card>

        {/* Class Performance Table */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.cardTitle}>
              Class Performance Overview
            </Text>
            <Text variant="bodyMedium" style={styles.cardSubtitle}>
              Detailed attendance statistics by class
            </Text>
            
            <View style={styles.tableContainer}>
              {classData.map((cls, index) => (
                <View key={index} style={styles.tableRow}>
                  <View style={styles.classInfo}>
                    <Text variant="titleMedium" style={styles.className}>
                      {cls.name}
                    </Text>
                    <Text variant="bodyMedium" style={styles.studentCount}>
                      {cls.students} students
                    </Text>
                  </View>
                  
                  <View style={styles.attendanceInfo}>
                    <View style={styles.attendanceRate}>
                      <Text variant="titleMedium" style={styles.attendancePercent}>
                        {cls.attendance}%
                      </Text>
                      <View style={styles.progressContainer}>
                        <View style={styles.progressBackground}>
                          <View 
                            style={[
                              styles.progressFill, 
                              { 
                                width: `${cls.attendance}%`,
                                backgroundColor: getStatusColor(cls.attendance)
                              }
                            ]} 
                          />
                        </View>
                      </View>
                    </View>
                    
                    <View style={styles.statusInfo}>
                      <Text variant="bodySmall" style={[styles.trend, { color: '#10b981' }]}>
                        {cls.trend}
                      </Text>
                      <View style={[
                        styles.statusBadge, 
                        { backgroundColor: `${getStatusColor(cls.attendance)}20` }
                      ]}>
                        <Text style={[styles.statusText, { color: getStatusColor(cls.attendance) }]}>
                          {getStatusText(cls.attendance)}
                        </Text>
                      </View>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 16,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    opacity: 0.7,
    marginBottom: 16,
  },
  exportButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  exportButton: {
    borderRadius: 8,
  },
  filterContainer: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  segmentedButtons: {
    backgroundColor: 'transparent',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 12,
    marginBottom: 16,
  },
  statCard: {
    width: (width - 52) / 2,
    elevation: 2,
  },
  statContent: {
    padding: 16,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  statInfo: {
    flex: 1,
  },
  statTitle: {
    opacity: 0.7,
    marginBottom: 4,
  },
  statValue: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statChange: {
    color: '#10b981',
    fontSize: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    margin: 20,
    marginTop: 0,
    elevation: 2,
  },
  cardTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  cardSubtitle: {
    opacity: 0.7,
    marginBottom: 16,
  },
  chartContainer: {
    alignItems: 'center',
    marginTop: 8,
  },
  chart: {
    borderRadius: 16,
  },
  tableContainer: {
    gap: 12,
    marginTop: 8,
  },
  tableRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
  },
  classInfo: {
    flex: 1,
  },
  className: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  studentCount: {
    opacity: 0.7,
  },
  attendanceInfo: {
    alignItems: 'flex-end',
    minWidth: 120,
  },
  attendanceRate: {
    alignItems: 'center',
    marginBottom: 8,
  },
  attendancePercent: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  progressContainer: {
    width: 80,
  },
  progressBackground: {
    height: 6,
    backgroundColor: '#e5e7eb',
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  statusInfo: {
    alignItems: 'flex-end',
    gap: 4,
  },
  trend: {
    fontSize: 12,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 11,
    fontWeight: '600',
  },
});
