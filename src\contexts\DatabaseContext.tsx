import React, { createContext, useContext, useEffect, useState } from 'react';
import { databaseService, User, Class, Student, AttendanceRecord } from '../services/simpleDatabase';

interface DatabaseContextType {
  isInitialized: boolean;
  currentUser: User | null;
  classes: Class[];
  students: Student[];
  
  // Auth methods
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
  
  // Class methods
  refreshClasses: () => Promise<void>;
  addClass: (classData: Omit<Class, 'id' | 'createdAt'>) => Promise<void>;
  updateClass: (id: number, classData: Partial<Omit<Class, 'id' | 'createdAt'>>) => Promise<void>;
  deleteClass: (id: number) => Promise<void>;
  
  // Student methods
  refreshStudents: () => Promise<void>;
  addStudent: (studentData: Omit<Student, 'id' | 'createdAt'>) => Promise<void>;
  updateStudent: (id: number, studentData: Partial<Omit<Student, 'id' | 'createdAt'>>) => Promise<void>;
  deleteStudent: (id: number) => Promise<void>;
  getStudentsByClass: (classId: number) => Promise<Student[]>;
  
  // Attendance methods
  recordAttendance: (attendanceData: Omit<AttendanceRecord, 'id' | 'createdAt'>) => Promise<void>;
  getAttendanceByClass: (classId: number, date?: string) => Promise<AttendanceRecord[]>;
  getAttendanceStats: (classId?: number, startDate?: string, endDate?: string) => Promise<any>;
  
  // Enrollment methods
  enrollStudentInClass: (classId: number, studentId: number) => Promise<void>;
  unenrollStudentFromClass: (classId: number, studentId: number) => Promise<void>;
}

const DatabaseContext = createContext<DatabaseContextType | undefined>(undefined);

export const useDatabaseContext = () => {
  const context = useContext(DatabaseContext);
  if (context === undefined) {
    throw new Error('useDatabaseContext must be used within a DatabaseProvider');
  }
  return context;
};

interface DatabaseProviderProps {
  children: React.ReactNode;
}

export const DatabaseProvider: React.FC<DatabaseProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [classes, setClasses] = useState<Class[]>([]);
  const [students, setStudents] = useState<Student[]>([]);

  useEffect(() => {
    initializeDatabase();
  }, []);

  const initializeDatabase = async () => {
    try {
      await databaseService.init();
      setIsInitialized(true);
      console.log('Database context initialized');
    } catch (error) {
      console.error('Failed to initialize database:', error);
    }
  };

  // Auth methods
  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const user = await databaseService.authenticateUser(email, password);
      if (user) {
        setCurrentUser(user);
        await refreshClasses();
        await refreshStudents();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  };

  const logout = () => {
    setCurrentUser(null);
    setClasses([]);
    setStudents([]);
  };

  // Class methods
  const refreshClasses = async () => {
    try {
      const allClasses = await databaseService.getAllClasses();
      setClasses(allClasses);
    } catch (error) {
      console.error('Failed to refresh classes:', error);
    }
  };

  const addClass = async (classData: Omit<Class, 'id' | 'createdAt'>) => {
    try {
      await databaseService.createClass(classData);
      await refreshClasses();
    } catch (error) {
      console.error('Failed to add class:', error);
      throw error;
    }
  };

  const updateClass = async (id: number, classData: Partial<Omit<Class, 'id' | 'createdAt'>>) => {
    try {
      await databaseService.updateClass(id, classData);
      await refreshClasses();
    } catch (error) {
      console.error('Failed to update class:', error);
      throw error;
    }
  };

  const deleteClass = async (id: number) => {
    try {
      await databaseService.deleteClass(id);
      await refreshClasses();
    } catch (error) {
      console.error('Failed to delete class:', error);
      throw error;
    }
  };

  // Student methods
  const refreshStudents = async () => {
    try {
      const allStudents = await databaseService.getAllStudents();
      setStudents(allStudents);
    } catch (error) {
      console.error('Failed to refresh students:', error);
    }
  };

  const addStudent = async (studentData: Omit<Student, 'id' | 'createdAt'>) => {
    try {
      await databaseService.createStudent(studentData);
      await refreshStudents();
    } catch (error) {
      console.error('Failed to add student:', error);
      throw error;
    }
  };

  const updateStudent = async (id: number, studentData: Partial<Omit<Student, 'id' | 'createdAt'>>) => {
    try {
      await databaseService.updateStudent(id, studentData);
      await refreshStudents();
    } catch (error) {
      console.error('Failed to update student:', error);
      throw error;
    }
  };

  const deleteStudent = async (id: number) => {
    try {
      await databaseService.deleteStudent(id);
      await refreshStudents();
    } catch (error) {
      console.error('Failed to delete student:', error);
      throw error;
    }
  };

  const getStudentsByClass = async (classId: number): Promise<Student[]> => {
    try {
      return await databaseService.getStudentsByClass(classId);
    } catch (error) {
      console.error('Failed to get students by class:', error);
      return [];
    }
  };

  // Attendance methods
  const recordAttendance = async (attendanceData: Omit<AttendanceRecord, 'id' | 'createdAt'>) => {
    try {
      await databaseService.recordAttendance(attendanceData);
    } catch (error) {
      console.error('Failed to record attendance:', error);
      throw error;
    }
  };

  const getAttendanceByClass = async (classId: number, date?: string): Promise<AttendanceRecord[]> => {
    try {
      return await databaseService.getAttendanceByClass(classId, date);
    } catch (error) {
      console.error('Failed to get attendance by class:', error);
      return [];
    }
  };

  const getAttendanceStats = async (classId?: number, startDate?: string, endDate?: string): Promise<any> => {
    try {
      return await databaseService.getAttendanceStats(classId, startDate, endDate);
    } catch (error) {
      console.error('Failed to get attendance stats:', error);
      return null;
    }
  };

  // Enrollment methods
  const enrollStudentInClass = async (classId: number, studentId: number) => {
    try {
      await databaseService.enrollStudentInClass(classId, studentId);
    } catch (error) {
      console.error('Failed to enroll student:', error);
      throw error;
    }
  };

  const unenrollStudentFromClass = async (classId: number, studentId: number) => {
    try {
      await databaseService.unenrollStudentFromClass(classId, studentId);
    } catch (error) {
      console.error('Failed to unenroll student:', error);
      throw error;
    }
  };

  const value: DatabaseContextType = {
    isInitialized,
    currentUser,
    classes,
    students,
    login,
    logout,
    refreshClasses,
    addClass,
    updateClass,
    deleteClass,
    refreshStudents,
    addStudent,
    updateStudent,
    deleteStudent,
    getStudentsByClass,
    recordAttendance,
    getAttendanceByClass,
    getAttendanceStats,
    enrollStudentInClass,
    unenrollStudentFromClass,
  };

  return (
    <DatabaseContext.Provider value={value}>
      {children}
    </DatabaseContext.Provider>
  );
};
