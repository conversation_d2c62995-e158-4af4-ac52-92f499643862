import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  FAB,
  Searchbar,
  Chip,
  IconButton,
  useTheme,
  Dialog,
  Portal,
  TextInput,
  Avatar,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface StudentData {
  id: string;
  name: string;
  studentId: string;
  email: string;
  course: string;
  year: string;
  status: 'active' | 'inactive';
  avatar?: string;
}

export default function ManageStudentsScreen() {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [dialogVisible, setDialogVisible] = useState(false);
  const [editingStudent, setEditingStudent] = useState<StudentData | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    studentId: '',
    email: '',
    course: '',
    year: '',
  });

  const [students, setStudents] = useState<StudentData[]>([
    {
      id: '1',
      name: 'Alice <PERSON>',
      studentId: 'CS2024001',
      email: '<EMAIL>',
      course: 'Computer Science',
      year: '3rd Year',
      status: 'active',
    },
    {
      id: '2',
      name: 'Bob Smith',
      studentId: 'CS2024002',
      email: '<EMAIL>',
      course: 'Computer Science',
      year: '2nd Year',
      status: 'active',
    },
    {
      id: '3',
      name: 'Carol Davis',
      studentId: 'CS2024003',
      email: '<EMAIL>',
      course: 'Information Technology',
      year: '4th Year',
      status: 'inactive',
    },
    {
      id: '4',
      name: 'David Brown',
      studentId: 'CS2024004',
      email: '<EMAIL>',
      course: 'Computer Science',
      year: '1st Year',
      status: 'active',
    },
  ]);

  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         student.studentId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         student.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterStatus === 'all' || student.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const handleAddStudent = () => {
    setEditingStudent(null);
    setFormData({ name: '', studentId: '', email: '', course: '', year: '' });
    setDialogVisible(true);
  };

  const handleEditStudent = (student: StudentData) => {
    setEditingStudent(student);
    setFormData({
      name: student.name,
      studentId: student.studentId,
      email: student.email,
      course: student.course,
      year: student.year,
    });
    setDialogVisible(true);
  };

  const handleSaveStudent = () => {
    if (!formData.name || !formData.studentId || !formData.email) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    if (editingStudent) {
      // Update existing student
      setStudents(prev => prev.map(student => 
        student.id === editingStudent.id 
          ? { ...student, ...formData }
          : student
      ));
    } else {
      // Add new student
      const newStudent: StudentData = {
        id: Date.now().toString(),
        ...formData,
        status: 'active',
      };
      setStudents(prev => [...prev, newStudent]);
    }

    setDialogVisible(false);
    setFormData({ name: '', studentId: '', email: '', course: '', year: '' });
  };

  const handleDeleteStudent = (studentId: string) => {
    Alert.alert(
      'Delete Student',
      'Are you sure you want to delete this student?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => setStudents(prev => prev.filter(student => student.id !== studentId)),
        },
      ]
    );
  };

  const toggleStudentStatus = (studentId: string) => {
    setStudents(prev => prev.map(student => 
      student.id === studentId 
        ? { ...student, status: student.status === 'active' ? 'inactive' : 'active' }
        : student
    ));
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.primary }]}>
          Manage Students
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Add and manage student information
        </Text>
      </View>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search students..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterContainer}>
          <Chip
            selected={filterStatus === 'all'}
            onPress={() => setFilterStatus('all')}
            style={styles.filterChip}
          >
            All ({students.length})
          </Chip>
          <Chip
            selected={filterStatus === 'active'}
            onPress={() => setFilterStatus('active')}
            style={styles.filterChip}
          >
            Active ({students.filter(s => s.status === 'active').length})
          </Chip>
          <Chip
            selected={filterStatus === 'inactive'}
            onPress={() => setFilterStatus('inactive')}
            style={styles.filterChip}
          >
            Inactive ({students.filter(s => s.status === 'inactive').length})
          </Chip>
        </ScrollView>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {filteredStudents.map((student) => (
          <Card key={student.id} style={styles.studentCard}>
            <Card.Content>
              <View style={styles.studentHeader}>
                <View style={styles.studentInfo}>
                  <Avatar.Text 
                    size={48} 
                    label={getInitials(student.name)}
                    style={[styles.avatar, { backgroundColor: theme.colors.secondary }]}
                    labelStyle={{ color: theme.colors.primary }}
                  />
                  <View style={styles.studentDetails}>
                    <Text variant="titleMedium" style={styles.studentName}>
                      {student.name}
                    </Text>
                    <Text variant="bodyMedium" style={styles.studentId}>
                      {student.studentId}
                    </Text>
                    <Text variant="bodySmall" style={styles.studentEmail}>
                      {student.email}
                    </Text>
                  </View>
                </View>
                <View style={styles.studentActions}>
                  <Chip
                    mode="outlined"
                    style={[
                      styles.statusChip,
                      { backgroundColor: student.status === 'active' ? theme.colors.secondary : theme.colors.surface }
                    ]}
                    textStyle={{ color: student.status === 'active' ? theme.colors.primary : theme.colors.onSurface }}
                  >
                    {student.status}
                  </Chip>
                </View>
              </View>

              <View style={styles.academicInfo}>
                <View style={styles.infoRow}>
                  <MaterialCommunityIcons name="school-outline" size={16} color={theme.colors.primary} />
                  <Text variant="bodySmall" style={styles.infoText}>
                    {student.course}
                  </Text>
                </View>
                <View style={styles.infoRow}>
                  <MaterialCommunityIcons name="calendar-outline" size={16} color={theme.colors.primary} />
                  <Text variant="bodySmall" style={styles.infoText}>
                    {student.year}
                  </Text>
                </View>
              </View>

              <View style={styles.actionButtons}>
                <Button
                  mode="outlined"
                  onPress={() => handleEditStudent(student)}
                  style={styles.actionButton}
                  icon="pencil"
                >
                  Edit
                </Button>
                <Button
                  mode="outlined"
                  onPress={() => toggleStudentStatus(student.id)}
                  style={styles.actionButton}
                  icon={student.status === 'active' ? 'pause' : 'play'}
                >
                  {student.status === 'active' ? 'Deactivate' : 'Activate'}
                </Button>
                <IconButton
                  icon="delete"
                  size={20}
                  onPress={() => handleDeleteStudent(student.id)}
                  iconColor={theme.colors.error}
                />
              </View>
            </Card.Content>
          </Card>
        ))}

        {filteredStudents.length === 0 && (
          <View style={styles.emptyState}>
            <MaterialCommunityIcons name="account-group-outline" size={64} color={theme.colors.outline} />
            <Text variant="titleMedium" style={styles.emptyTitle}>
              No students found
            </Text>
            <Text variant="bodyMedium" style={styles.emptySubtitle}>
              {searchQuery ? 'Try adjusting your search' : 'Add your first student to get started'}
            </Text>
          </View>
        )}
      </ScrollView>

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.secondary }]}
        onPress={handleAddStudent}
        label="Add Student"
      />

      <Portal>
        <Dialog visible={dialogVisible} onDismiss={() => setDialogVisible(false)}>
          <Dialog.Title>{editingStudent ? 'Edit Student' : 'Add New Student'}</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Full Name *"
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              style={styles.input}
              mode="outlined"
            />
            <TextInput
              label="Student ID *"
              value={formData.studentId}
              onChangeText={(text) => setFormData(prev => ({ ...prev, studentId: text }))}
              style={styles.input}
              mode="outlined"
            />
            <TextInput
              label="Email *"
              value={formData.email}
              onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
              style={styles.input}
              mode="outlined"
              keyboardType="email-address"
            />
            <TextInput
              label="Course"
              value={formData.course}
              onChangeText={(text) => setFormData(prev => ({ ...prev, course: text }))}
              style={styles.input}
              mode="outlined"
            />
            <TextInput
              label="Year Level"
              value={formData.year}
              onChangeText={(text) => setFormData(prev => ({ ...prev, year: text }))}
              style={styles.input}
              mode="outlined"
              placeholder="e.g., 1st Year, 2nd Year"
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setDialogVisible(false)}>Cancel</Button>
            <Button onPress={handleSaveStudent} mode="contained">
              {editingStudent ? 'Update' : 'Add'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    opacity: 0.7,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  searchbar: {
    marginBottom: 10,
  },
  filterContainer: {
    flexDirection: 'row',
  },
  filterChip: {
    marginRight: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  studentCard: {
    marginBottom: 12,
    elevation: 2,
  },
  studentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  studentInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  avatar: {
    marginRight: 12,
  },
  studentDetails: {
    flex: 1,
  },
  studentName: {
    fontWeight: 'bold',
    marginBottom: 2,
  },
  studentId: {
    opacity: 0.7,
    marginBottom: 2,
  },
  studentEmail: {
    opacity: 0.6,
  },
  studentActions: {
    marginLeft: 12,
  },
  statusChip: {
    height: 28,
  },
  academicInfo: {
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  infoText: {
    marginLeft: 8,
    opacity: 0.8,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  input: {
    marginBottom: 12,
  },
});
