import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  FAB,
  Searchbar,
  Chip,
  IconButton,
  useTheme,
  Dialog,
  Portal,
  TextInput,
  Avatar,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useDatabaseContext } from '../contexts/DatabaseContext';

export default function ManageStudentsScreen() {
  const theme = useTheme();
  const { students, addStudent, updateStudent, deleteStudent, refreshStudents } = useDatabaseContext();
  const [searchQuery, setSearchQuery] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'active' | 'inactive'>('all');
  const [dialogVisible, setDialogVisible] = useState(false);
  const [editingStudent, setEditingStudent] = useState<any>(null);
  const [formData, setFormData] = useState({
    name: '',
    studentId: '',
    email: '',
    course: '',
    year: '',
  });

  useEffect(() => {
    refreshStudents();
  }, []);

  const filteredStudents = students.filter(student => {
    const matchesSearch = student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         student.studentId.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         student.email.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterStatus === 'all' || student.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const handleAddStudent = () => {
    setEditingStudent(null);
    setFormData({ name: '', studentId: '', email: '', course: '', year: '' });
    setDialogVisible(true);
  };

  const handleEditStudent = (student: any) => {
    setEditingStudent(student);
    setFormData({
      name: student.name,
      studentId: student.studentId,
      email: student.email,
      course: student.course,
      year: student.year,
    });
    setDialogVisible(true);
  };

  const handleSaveStudent = async () => {
    if (!formData.name || !formData.studentId || !formData.email) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    try {
      if (editingStudent) {
        // Update existing student
        await updateStudent(editingStudent.id, formData);
      } else {
        // Add new student
        await addStudent({
          ...formData,
          status: 'active',
        });
      }

      setDialogVisible(false);
      setFormData({ name: '', studentId: '', email: '', course: '', year: '' });
      Alert.alert('Success', editingStudent ? 'Student updated successfully' : 'Student added successfully');
    } catch (error) {
      Alert.alert('Error', 'Failed to save student. Please try again.');
      console.error('Save student error:', error);
    }
  };

  const handleDeleteStudent = (studentId: number) => {
    Alert.alert(
      'Delete Student',
      'Are you sure you want to delete this student?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteStudent(studentId);
              Alert.alert('Success', 'Student deleted successfully');
            } catch (error) {
              Alert.alert('Error', 'Failed to delete student');
            }
          },
        },
      ]
    );
  };

  const toggleStudentStatus = async (studentId: number, currentStatus: string) => {
    try {
      await updateStudent(studentId, {
        status: currentStatus === 'active' ? 'inactive' : 'active'
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to update student status');
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.primary }]}>
          Manage Students
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Add and manage student information
        </Text>
      </View>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder="Search students..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />
        
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filterContainer}>
          <Chip
            selected={filterStatus === 'all'}
            onPress={() => setFilterStatus('all')}
            style={styles.filterChip}
          >
            All ({students.length})
          </Chip>
          <Chip
            selected={filterStatus === 'active'}
            onPress={() => setFilterStatus('active')}
            style={styles.filterChip}
          >
            Active ({students.filter(s => s.status === 'active').length})
          </Chip>
          <Chip
            selected={filterStatus === 'inactive'}
            onPress={() => setFilterStatus('inactive')}
            style={styles.filterChip}
          >
            Inactive ({students.filter(s => s.status === 'inactive').length})
          </Chip>
        </ScrollView>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {filteredStudents.map((student) => (
          <Card key={student.id} style={styles.studentCard}>
            <Card.Content>
              <View style={styles.studentHeader}>
                <View style={styles.studentInfo}>
                  <Avatar.Text 
                    size={48} 
                    label={getInitials(student.name)}
                    style={[styles.avatar, { backgroundColor: theme.colors.secondary }]}
                    labelStyle={{ color: theme.colors.primary }}
                  />
                  <View style={styles.studentDetails}>
                    <Text variant="titleMedium" style={styles.studentName}>
                      {student.name}
                    </Text>
                    <Text variant="bodyMedium" style={styles.studentId}>
                      {student.studentId}
                    </Text>
                    <Text variant="bodySmall" style={styles.studentEmail}>
                      {student.email}
                    </Text>
                  </View>
                </View>
                <View style={styles.studentActions}>
                  <Chip
                    mode="outlined"
                    style={[
                      styles.statusChip,
                      { backgroundColor: student.status === 'active' ? theme.colors.secondary : theme.colors.surface }
                    ]}
                    textStyle={{ color: student.status === 'active' ? theme.colors.primary : theme.colors.onSurface }}
                  >
                    {student.status}
                  </Chip>
                </View>
              </View>

              <View style={styles.academicInfo}>
                <View style={styles.infoRow}>
                  <MaterialCommunityIcons name="school-outline" size={16} color={theme.colors.primary} />
                  <Text variant="bodySmall" style={styles.infoText}>
                    {student.course}
                  </Text>
                </View>
                <View style={styles.infoRow}>
                  <MaterialCommunityIcons name="calendar-outline" size={16} color={theme.colors.primary} />
                  <Text variant="bodySmall" style={styles.infoText}>
                    {student.year}
                  </Text>
                </View>
              </View>

              <View style={styles.actionButtons}>
                <Button
                  mode="outlined"
                  onPress={() => handleEditStudent(student)}
                  style={styles.actionButton}
                  icon="pencil"
                >
                  Edit
                </Button>
                <Button
                  mode="outlined"
                  onPress={() => toggleStudentStatus(student.id, student.status)}
                  style={styles.actionButton}
                  icon={student.status === 'active' ? 'pause' : 'play'}
                >
                  {student.status === 'active' ? 'Deactivate' : 'Activate'}
                </Button>
                <IconButton
                  icon="delete"
                  size={20}
                  onPress={() => handleDeleteStudent(student.id)}
                  iconColor={theme.colors.error}
                />
              </View>
            </Card.Content>
          </Card>
        ))}

        {filteredStudents.length === 0 && (
          <View style={styles.emptyState}>
            <MaterialCommunityIcons name="account-group-outline" size={64} color={theme.colors.outline} />
            <Text variant="titleMedium" style={styles.emptyTitle}>
              No students found
            </Text>
            <Text variant="bodyMedium" style={styles.emptySubtitle}>
              {searchQuery ? 'Try adjusting your search' : 'Add your first student to get started'}
            </Text>
          </View>
        )}
      </ScrollView>

      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.secondary }]}
        onPress={handleAddStudent}
        label="Add Student"
      />

      <Portal>
        <Dialog visible={dialogVisible} onDismiss={() => setDialogVisible(false)}>
          <Dialog.Title>{editingStudent ? 'Edit Student' : 'Add New Student'}</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Full Name *"
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              style={styles.input}
              mode="outlined"
            />
            <TextInput
              label="Student ID *"
              value={formData.studentId}
              onChangeText={(text) => setFormData(prev => ({ ...prev, studentId: text }))}
              style={styles.input}
              mode="outlined"
            />
            <TextInput
              label="Email *"
              value={formData.email}
              onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
              style={styles.input}
              mode="outlined"
              keyboardType="email-address"
            />
            <TextInput
              label="Course"
              value={formData.course}
              onChangeText={(text) => setFormData(prev => ({ ...prev, course: text }))}
              style={styles.input}
              mode="outlined"
            />
            <TextInput
              label="Year Level"
              value={formData.year}
              onChangeText={(text) => setFormData(prev => ({ ...prev, year: text }))}
              style={styles.input}
              mode="outlined"
              placeholder="e.g., 1st Year, 2nd Year"
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setDialogVisible(false)}>Cancel</Button>
            <Button onPress={handleSaveStudent} mode="contained">
              {editingStudent ? 'Update' : 'Add'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    opacity: 0.7,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  searchbar: {
    marginBottom: 10,
  },
  filterContainer: {
    flexDirection: 'row',
  },
  filterChip: {
    marginRight: 8,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  studentCard: {
    marginBottom: 12,
    elevation: 2,
  },
  studentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  studentInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  avatar: {
    marginRight: 12,
  },
  studentDetails: {
    flex: 1,
  },
  studentName: {
    fontWeight: 'bold',
    marginBottom: 2,
  },
  studentId: {
    opacity: 0.7,
    marginBottom: 2,
  },
  studentEmail: {
    opacity: 0.6,
  },
  studentActions: {
    marginLeft: 12,
  },
  statusChip: {
    height: 28,
  },
  academicInfo: {
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  infoText: {
    marginLeft: 8,
    opacity: 0.8,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
  },
  emptyTitle: {
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  input: {
    marginBottom: 12,
  },
});
