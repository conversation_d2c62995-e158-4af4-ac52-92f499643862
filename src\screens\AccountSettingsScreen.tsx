import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  List,
  Switch,
  useTheme,
  Dialog,
  Portal,
  TextInput,
  Avatar,
  Divider,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function AccountSettingsScreen({ navigation }: any) {
  const theme = useTheme();
  const [user, setUser] = useState({
    name: 'Dr. <PERSON>',
    email: '<EMAIL>',
    department: 'Computer Science',
    employeeId: 'EMP2024001',
  });

  const [settings, setSettings] = useState({
    notifications: true,
    emailAlerts: false,
    autoBackup: true,
    darkMode: false,
    faceRecognition: true,
  });

  const [dialogVisible, setDialogVisible] = useState(false);
  const [dialogType, setDialogType] = useState<'profile' | 'password' | 'logout'>('profile');
  const [formData, setFormData] = useState({
    name: user.name,
    email: user.email,
    department: user.department,
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });

  const handleSettingChange = (key: keyof typeof settings) => {
    setSettings(prev => ({ ...prev, [key]: !prev[key] }));
  };

  const handleEditProfile = () => {
    setDialogType('profile');
    setFormData({
      ...formData,
      name: user.name,
      email: user.email,
      department: user.department,
    });
    setDialogVisible(true);
  };

  const handleChangePassword = () => {
    setDialogType('password');
    setFormData({
      ...formData,
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
    setDialogVisible(true);
  };

  const handleLogout = () => {
    setDialogType('logout');
    setDialogVisible(true);
  };

  const handleSaveProfile = () => {
    if (!formData.name || !formData.email) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    setUser({
      ...user,
      name: formData.name,
      email: formData.email,
      department: formData.department,
    });
    setDialogVisible(false);
    Alert.alert('Success', 'Profile updated successfully');
  };

  const handleSavePassword = () => {
    if (!formData.currentPassword || !formData.newPassword || !formData.confirmPassword) {
      Alert.alert('Error', 'Please fill in all password fields');
      return;
    }

    if (formData.newPassword !== formData.confirmPassword) {
      Alert.alert('Error', 'New passwords do not match');
      return;
    }

    if (formData.newPassword.length < 6) {
      Alert.alert('Error', 'Password must be at least 6 characters long');
      return;
    }

    setDialogVisible(false);
    Alert.alert('Success', 'Password changed successfully');
  };

  const handleConfirmLogout = async () => {
    try {
      await AsyncStorage.removeItem('isAuthenticated');
      setDialogVisible(false);
      // Navigate to login screen
      navigation.reset({
        index: 0,
        routes: [{ name: 'Login' }],
      });
    } catch (error) {
      Alert.alert('Error', 'Failed to logout');
    }
  };

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase();
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.primary }]}>
          Account Settings
        </Text>
        <Text variant="bodyMedium" style={styles.subtitle}>
          Manage your profile and preferences
        </Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <Card style={styles.profileCard}>
          <Card.Content>
            <View style={styles.profileHeader}>
              <Avatar.Text 
                size={64} 
                label={getInitials(user.name)}
                style={[styles.avatar, { backgroundColor: theme.colors.secondary }]}
                labelStyle={{ color: theme.colors.primary, fontSize: 24 }}
              />
              <View style={styles.profileInfo}>
                <Text variant="titleLarge" style={styles.userName}>
                  {user.name}
                </Text>
                <Text variant="bodyMedium" style={styles.userEmail}>
                  {user.email}
                </Text>
                <Text variant="bodySmall" style={styles.userDepartment}>
                  {user.department} • {user.employeeId}
                </Text>
              </View>
            </View>
            <Button
              mode="outlined"
              onPress={handleEditProfile}
              style={styles.editButton}
              icon="pencil"
            >
              Edit Profile
            </Button>
          </Card.Content>
        </Card>

        {/* Account Settings */}
        <Card style={styles.settingsCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Account
            </Text>
            <List.Item
              title="Change Password"
              description="Update your account password"
              left={(props) => <List.Icon {...props} icon="lock-outline" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={handleChangePassword}
            />
            <Divider />
            <List.Item
              title="Two-Factor Authentication"
              description="Add an extra layer of security"
              left={(props) => <List.Icon {...props} icon="shield-check-outline" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => Alert.alert('Coming Soon', 'This feature will be available soon')}
            />
          </Card.Content>
        </Card>

        {/* App Settings */}
        <Card style={styles.settingsCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              App Settings
            </Text>
            <List.Item
              title="Push Notifications"
              description="Receive attendance alerts"
              left={(props) => <List.Icon {...props} icon="bell-outline" />}
              right={() => (
                <Switch
                  value={settings.notifications}
                  onValueChange={() => handleSettingChange('notifications')}
                />
              )}
            />
            <Divider />
            <List.Item
              title="Email Alerts"
              description="Get email notifications for important events"
              left={(props) => <List.Icon {...props} icon="email-outline" />}
              right={() => (
                <Switch
                  value={settings.emailAlerts}
                  onValueChange={() => handleSettingChange('emailAlerts')}
                />
              )}
            />
            <Divider />
            <List.Item
              title="Auto Backup"
              description="Automatically backup attendance data"
              left={(props) => <List.Icon {...props} icon="backup-restore" />}
              right={() => (
                <Switch
                  value={settings.autoBackup}
                  onValueChange={() => handleSettingChange('autoBackup')}
                />
              )}
            />
            <Divider />
            <List.Item
              title="Face Recognition"
              description="Enable facial recognition for attendance"
              left={(props) => <List.Icon {...props} icon="face-recognition" />}
              right={() => (
                <Switch
                  value={settings.faceRecognition}
                  onValueChange={() => handleSettingChange('faceRecognition')}
                />
              )}
            />
          </Card.Content>
        </Card>

        {/* Support & Info */}
        <Card style={styles.settingsCard}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Support & Information
            </Text>
            <List.Item
              title="Help & Support"
              description="Get help with using the app"
              left={(props) => <List.Icon {...props} icon="help-circle-outline" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => Alert.alert('Help', 'Contact <NAME_EMAIL>')}
            />
            <Divider />
            <List.Item
              title="Privacy Policy"
              description="Read our privacy policy"
              left={(props) => <List.Icon {...props} icon="shield-account-outline" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => Alert.alert('Privacy Policy', 'Privacy policy will be displayed here')}
            />
            <Divider />
            <List.Item
              title="About"
              description="App version 1.0.0"
              left={(props) => <List.Icon {...props} icon="information-outline" />}
              right={(props) => <List.Icon {...props} icon="chevron-right" />}
              onPress={() => Alert.alert('About', 'AttendWise v1.0.0\nDeveloped for USTP')}
            />
          </Card.Content>
        </Card>

        {/* Logout */}
        <Card style={[styles.settingsCard, styles.logoutCard]}>
          <Card.Content>
            <List.Item
              title="Logout"
              description="Sign out of your account"
              left={(props) => <List.Icon {...props} icon="logout" color={theme.colors.error} />}
              titleStyle={{ color: theme.colors.error }}
              onPress={handleLogout}
            />
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Dialogs */}
      <Portal>
        {/* Edit Profile Dialog */}
        <Dialog visible={dialogVisible && dialogType === 'profile'} onDismiss={() => setDialogVisible(false)}>
          <Dialog.Title>Edit Profile</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Full Name"
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              style={styles.input}
              mode="outlined"
            />
            <TextInput
              label="Email"
              value={formData.email}
              onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
              style={styles.input}
              mode="outlined"
              keyboardType="email-address"
            />
            <TextInput
              label="Department"
              value={formData.department}
              onChangeText={(text) => setFormData(prev => ({ ...prev, department: text }))}
              style={styles.input}
              mode="outlined"
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setDialogVisible(false)}>Cancel</Button>
            <Button onPress={handleSaveProfile} mode="contained">Save</Button>
          </Dialog.Actions>
        </Dialog>

        {/* Change Password Dialog */}
        <Dialog visible={dialogVisible && dialogType === 'password'} onDismiss={() => setDialogVisible(false)}>
          <Dialog.Title>Change Password</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Current Password"
              value={formData.currentPassword}
              onChangeText={(text) => setFormData(prev => ({ ...prev, currentPassword: text }))}
              style={styles.input}
              mode="outlined"
              secureTextEntry
            />
            <TextInput
              label="New Password"
              value={formData.newPassword}
              onChangeText={(text) => setFormData(prev => ({ ...prev, newPassword: text }))}
              style={styles.input}
              mode="outlined"
              secureTextEntry
            />
            <TextInput
              label="Confirm New Password"
              value={formData.confirmPassword}
              onChangeText={(text) => setFormData(prev => ({ ...prev, confirmPassword: text }))}
              style={styles.input}
              mode="outlined"
              secureTextEntry
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setDialogVisible(false)}>Cancel</Button>
            <Button onPress={handleSavePassword} mode="contained">Change Password</Button>
          </Dialog.Actions>
        </Dialog>

        {/* Logout Confirmation Dialog */}
        <Dialog visible={dialogVisible && dialogType === 'logout'} onDismiss={() => setDialogVisible(false)}>
          <Dialog.Title>Logout</Dialog.Title>
          <Dialog.Content>
            <Text variant="bodyMedium">Are you sure you want to logout?</Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setDialogVisible(false)}>Cancel</Button>
            <Button onPress={handleConfirmLogout} mode="contained">Logout</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  subtitle: {
    opacity: 0.7,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
  },
  profileCard: {
    marginBottom: 16,
    elevation: 2,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  avatar: {
    marginRight: 16,
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  userEmail: {
    opacity: 0.7,
    marginBottom: 2,
  },
  userDepartment: {
    opacity: 0.6,
  },
  editButton: {
    alignSelf: 'flex-start',
  },
  settingsCard: {
    marginBottom: 16,
    elevation: 2,
  },
  logoutCard: {
    marginBottom: 40,
  },
  sectionTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  input: {
    marginBottom: 12,
  },
});
