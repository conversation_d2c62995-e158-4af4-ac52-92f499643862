
import React, { useState } from 'react';
import { Plus, Edit, Trash2, Users, Calendar, Clock } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/hooks/use-toast';
import Navbar from '@/components/Navbar';

interface Class {
  id: string;
  name: string;
  subject: string;
  schedule: string;
  time: string;
  students: number;
  room: string;
}

const Classes = () => {
  const [classes, setClasses] = useState<Class[]>([
    {
      id: '1',
      name: 'Computer Science 101',
      subject: 'Computer Science',
      schedule: 'Mon, Wed, Fri',
      time: '9:00 AM - 10:30 AM',
      students: 28,
      room: 'Room 204'
    },
    {
      id: '2',
      name: 'Data Structures',
      subject: 'Computer Science',
      schedule: 'Tue, Thu',
      time: '11:00 AM - 12:30 PM',
      students: 24,
      room: 'Room 301'
    },
    {
      id: '3',
      name: 'Machine Learning',
      subject: 'AI & Data Science',
      schedule: 'Mon, Wed',
      time: '2:00 PM - 3:30 PM',
      students: 32,
      room: 'Lab 105'
    },
    {
      id: '4',
      name: 'Web Development',
      subject: 'Computer Science',
      schedule: 'Tue, Thu, Fri',
      time: '4:00 PM - 5:30 PM',
      students: 22,
      room: 'Lab 203'
    }
  ]);

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingClass, setEditingClass] = useState<Class | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    schedule: '',
    time: '',
    room: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (editingClass) {
      setClasses(classes.map(cls => 
        cls.id === editingClass.id 
          ? { ...cls, ...formData }
          : cls
      ));
      toast({
        title: "Class updated successfully",
        description: `${formData.name} has been updated.`,
      });
    } else {
      const newClass: Class = {
        id: Date.now().toString(),
        ...formData,
        students: 0
      };
      setClasses([...classes, newClass]);
      toast({
        title: "Class created successfully",
        description: `${formData.name} has been added to your classes.`,
      });
    }
    
    setIsDialogOpen(false);
    setEditingClass(null);
    setFormData({ name: '', subject: '', schedule: '', time: '', room: '' });
  };

  const handleEdit = (cls: Class) => {
    setEditingClass(cls);
    setFormData({
      name: cls.name,
      subject: cls.subject,
      schedule: cls.schedule,
      time: cls.time,
      room: cls.room
    });
    setIsDialogOpen(true);
  };

  const handleDelete = (id: string) => {
    setClasses(classes.filter(cls => cls.id !== id));
    toast({
      title: "Class deleted",
      description: "The class has been removed from your list.",
      variant: "destructive",
    });
  };

  const openNewClassDialog = () => {
    setEditingClass(null);
    setFormData({ name: '', subject: '', schedule: '', time: '', room: '' });
    setIsDialogOpen(true);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Classes</h1>
            <p className="text-gray-600 mt-2">
              Manage your class schedules and student enrollments
            </p>
          </div>
          
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={openNewClassDialog} className="bg-blue-600 hover:bg-blue-700">
                <Plus className="w-4 h-4 mr-2" />
                Add New Class
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-md">
              <DialogHeader>
                <DialogTitle>
                  {editingClass ? 'Edit Class' : 'Create New Class'}
                </DialogTitle>
                <DialogDescription>
                  {editingClass ? 'Update the class information below.' : 'Fill in the details to create a new class.'}
                </DialogDescription>
              </DialogHeader>
              <form onSubmit={handleSubmit} className="space-y-4">
                <div>
                  <Label htmlFor="name">Class Name</Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    placeholder="e.g., Computer Science 101"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="subject">Subject</Label>
                  <Select value={formData.subject} onValueChange={(value) => setFormData({ ...formData, subject: value })}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a subject" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Computer Science">Computer Science</SelectItem>
                      <SelectItem value="Mathematics">Mathematics</SelectItem>
                      <SelectItem value="Physics">Physics</SelectItem>
                      <SelectItem value="Chemistry">Chemistry</SelectItem>
                      <SelectItem value="AI & Data Science">AI & Data Science</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="schedule">Schedule</Label>
                  <Input
                    id="schedule"
                    value={formData.schedule}
                    onChange={(e) => setFormData({ ...formData, schedule: e.target.value })}
                    placeholder="e.g., Mon, Wed, Fri"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="time">Time</Label>
                  <Input
                    id="time"
                    value={formData.time}
                    onChange={(e) => setFormData({ ...formData, time: e.target.value })}
                    placeholder="e.g., 9:00 AM - 10:30 AM"
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="room">Room</Label>
                  <Input
                    id="room"
                    value={formData.room}
                    onChange={(e) => setFormData({ ...formData, room: e.target.value })}
                    placeholder="e.g., Room 204"
                    required
                  />
                </div>
                <Button type="submit" className="w-full">
                  {editingClass ? 'Update Class' : 'Create Class'}
                </Button>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {classes.map((cls) => (
            <Card key={cls.id} className="transition-all hover:shadow-lg">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{cls.name}</CardTitle>
                    <CardDescription>{cls.subject}</CardDescription>
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEdit(cls)}
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(cls.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center text-sm text-gray-600">
                    <Calendar className="w-4 h-4 mr-2" />
                    {cls.schedule}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Clock className="w-4 h-4 mr-2" />
                    {cls.time}
                  </div>
                  <div className="flex items-center text-sm text-gray-600">
                    <Users className="w-4 h-4 mr-2" />
                    {cls.students} students enrolled
                  </div>
                  <div className="text-sm text-gray-600">
                    📍 {cls.room}
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t">
                  <Button variant="outline" className="w-full">
                    View Details
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Classes;
