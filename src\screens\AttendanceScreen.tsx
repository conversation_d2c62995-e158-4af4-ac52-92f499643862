import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert, Dimensions } from 'react-native';
import {
  Text,
  Card,
  Button,
  useTheme,
  Avatar,
  ProgressBar,
  Chip
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';

const { width } = Dimensions.get('window');

interface AttendanceRecord {
  id: string;
  name: string;
  studentId: string;
  status: 'present' | 'absent' | 'pending';
  timestamp?: string;
  confidence?: number;
}

export default function AttendanceScreen() {
  const theme = useTheme();
  const [selectedClass, setSelectedClass] = useState('Computer Science 101');
  const [isRecording, setIsRecording] = useState(false);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([
    { id: '1', name: '<PERSON>', studentId: 'CS2024001', status: 'pending' },
    { id: '2', name: '<PERSON>', studentId: 'CS2024002', status: 'pending' },
    { id: '3', name: '<PERSON>', studentId: 'CS2024003', status: 'pending' },
    { id: '4', name: 'David <PERSON>', studentId: 'CS2024004', status: 'pending' },
  ]);

  const classes = [
    'Computer Science 101',
    'Data Structures',
    'Machine Learning',
    'Web Development'
  ];

  const presentCount = attendanceRecords.filter(r => r.status === 'present').length;
  const totalStudents = attendanceRecords.length;
  const attendancePercentage = totalStudents > 0 ? (presentCount / totalStudents) * 100 : 0;

  const startRecognition = () => {
    setIsRecording(true);
    Alert.alert(
      'Facial Recognition Started',
      'Students can now stand in front of the camera for attendance.'
    );

    // Simulate facial recognition process
    let recognizedCount = 0;
    const interval = setInterval(() => {
      const pendingStudents = attendanceRecords.filter(r => r.status === 'pending');
      if (pendingStudents.length > 0 && recognizedCount < 3) {
        const randomStudent = pendingStudents[Math.floor(Math.random() * pendingStudents.length)];
        setAttendanceRecords(prev => prev.map(record =>
          record.id === randomStudent.id
            ? {
                ...record,
                status: 'present',
                timestamp: new Date().toLocaleTimeString(),
                confidence: 92 + Math.floor(Math.random() * 8)
              }
            : record
        ));
        recognizedCount++;

        Alert.alert('Student Recognized', `${randomStudent.name} marked as present.`);
      }
    }, 2000);

    // Auto-stop after 10 seconds for demo
    setTimeout(() => {
      clearInterval(interval);
      setIsRecording(false);
    }, 10000);
  };

  const stopRecognition = () => {
    setIsRecording(false);
    Alert.alert('Attendance Session Ended', 'Facial recognition has been stopped.');
  };

  const markManually = (id: string, status: 'present' | 'absent') => {
    setAttendanceRecords(prev => prev.map(record =>
      record.id === id
        ? {
            ...record,
            status,
            timestamp: new Date().toLocaleTimeString()
          }
        : record
    ));

    const student = attendanceRecords.find(r => r.id === id);
    Alert.alert('Success', `${student?.name} marked as ${status}`);
  };

  const resetAttendance = () => {
    setAttendanceRecords(prev => prev.map(record => ({
      ...record,
      status: 'pending' as const,
      timestamp: undefined,
      confidence: undefined
    })));
    Alert.alert('Success', 'All attendance records have been cleared.');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text variant="headlineMedium" style={styles.title}>
            Attendance
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            Use AI-powered facial recognition to mark attendance
          </Text>
        </View>

        {/* Class Selection */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.cardTitle}>
              Selected Class
            </Text>
            <View style={styles.classSelector}>
              <MaterialCommunityIcons
                name="book-open-variant"
                size={20}
                color={theme.colors.primary}
              />
              <Text variant="bodyLarge" style={styles.selectedClass}>
                {selectedClass}
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Camera Simulation */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.cardTitle}>
              Facial Recognition Camera
            </Text>
            <Text variant="bodyMedium" style={styles.cardSubtitle}>
              Students should stand in front of the camera one at a time
            </Text>

            <View style={[styles.cameraView, { backgroundColor: isRecording ? '#1f2937' : '#374151' }]}>
              {isRecording ? (
                <View style={styles.recordingView}>
                  <View style={styles.recordingIndicator}>
                    <MaterialCommunityIcons name="camera" size={32} color="white" />
                  </View>
                  <Text variant="titleMedium" style={styles.recordingText}>
                    Facial Recognition Active
                  </Text>
                  <Text variant="bodyMedium" style={styles.recordingSubtext}>
                    Looking for faces...
                  </Text>
                  <View style={styles.recordingBadge}>
                    <View style={styles.recordingDot} />
                    <Text style={styles.recordingLabel}>RECORDING</Text>
                  </View>
                </View>
              ) : (
                <View style={styles.cameraReady}>
                  <MaterialCommunityIcons name="camera" size={48} color="#9ca3af" />
                  <Text variant="titleMedium" style={styles.cameraReadyText}>
                    Camera Ready
                  </Text>
                  <Text variant="bodyMedium" style={styles.cameraReadySubtext}>
                    Click "Start Recognition" to begin
                  </Text>
                </View>
              )}
            </View>

            <View style={styles.controls}>
              {!isRecording ? (
                <Button
                  mode="contained"
                  onPress={startRecognition}
                  style={[styles.controlButton, { backgroundColor: '#10b981' }]}
                  icon="play"
                >
                  Start Recognition
                </Button>
              ) : (
                <Button
                  mode="contained"
                  onPress={stopRecognition}
                  style={[styles.controlButton, { backgroundColor: '#ef4444' }]}
                  icon="stop"
                >
                  Stop Recognition
                </Button>
              )}

              <Button
                mode="outlined"
                onPress={resetAttendance}
                style={styles.resetButton}
                icon="refresh"
              >
                Reset
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* Attendance Summary */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.cardTitle}>
              Session Summary
            </Text>

            <View style={styles.summaryStats}>
              <Text variant="bodyMedium" style={styles.summaryLabel}>
                Attendance Rate: {attendancePercentage.toFixed(1)}%
              </Text>
              <ProgressBar
                progress={attendancePercentage / 100}
                style={styles.progressBar}
                color={theme.colors.primary}
              />
            </View>

            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text variant="headlineSmall" style={[styles.statNumber, { color: '#10b981' }]}>
                  {presentCount}
                </Text>
                <Text variant="bodySmall" style={styles.statLabel}>Present</Text>
              </View>
              <View style={styles.statItem}>
                <Text variant="headlineSmall" style={[styles.statNumber, { color: '#6b7280' }]}>
                  {totalStudents - presentCount}
                </Text>
                <Text variant="bodySmall" style={styles.statLabel}>Pending</Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Students List */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.cardTitle}>
              Students
            </Text>
            <Text variant="bodyMedium" style={styles.cardSubtitle}>
              Real-time attendance status
            </Text>

            <View style={styles.studentsList}>
              {attendanceRecords.map((record) => (
                <View key={record.id} style={styles.studentItem}>
                  <View style={styles.studentInfo}>
                    <Avatar.Text
                      size={40}
                      label={record.name.split(' ').map(n => n[0]).join('')}
                      style={[styles.studentAvatar, { backgroundColor: theme.colors.primary }]}
                      labelStyle={{ color: theme.colors.secondary, fontSize: 14 }}
                    />
                    <View style={styles.studentDetails}>
                      <Text variant="bodyMedium" style={styles.studentName}>
                        {record.name}
                      </Text>
                      <Text variant="bodySmall" style={styles.studentId}>
                        {record.studentId}
                      </Text>
                      {record.timestamp && (
                        <Text variant="bodySmall" style={styles.timestamp}>
                          {record.timestamp}
                        </Text>
                      )}
                    </View>
                  </View>

                  <View style={styles.studentStatus}>
                    {record.status === 'present' && (
                      <View style={styles.statusContainer}>
                        <MaterialCommunityIcons name="check-circle" size={20} color="#10b981" />
                        {record.confidence && (
                          <Text style={styles.confidence}>{record.confidence}%</Text>
                        )}
                      </View>
                    )}
                    {record.status === 'absent' && (
                      <MaterialCommunityIcons name="close-circle" size={20} color="#ef4444" />
                    )}
                    {record.status === 'pending' && (
                      <View style={styles.manualButtons}>
                        <Button
                          mode="outlined"
                          compact
                          onPress={() => markManually(record.id, 'present')}
                          style={styles.manualButton}
                        >
                          ✓
                        </Button>
                        <Button
                          mode="outlined"
                          compact
                          onPress={() => markManually(record.id, 'absent')}
                          style={styles.manualButton}
                        >
                          ✗
                        </Button>
                      </View>
                    )}
                  </View>
                </View>
              ))}
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 16,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    opacity: 0.7,
  },
  card: {
    margin: 20,
    marginTop: 16,
    elevation: 2,
  },
  cardTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  cardSubtitle: {
    opacity: 0.7,
    marginBottom: 16,
  },
  classSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 8,
  },
  selectedClass: {
    fontWeight: '600',
  },
  cameraView: {
    height: 200,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 16,
    position: 'relative',
  },
  recordingView: {
    alignItems: 'center',
  },
  recordingIndicator: {
    width: 60,
    height: 60,
    backgroundColor: '#ef4444',
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  recordingText: {
    color: 'white',
    marginBottom: 8,
  },
  recordingSubtext: {
    color: 'white',
    opacity: 0.8,
  },
  recordingBadge: {
    position: 'absolute',
    top: 16,
    left: 16,
    backgroundColor: '#ef4444',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  recordingDot: {
    width: 8,
    height: 8,
    backgroundColor: 'white',
    borderRadius: 4,
  },
  recordingLabel: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  cameraReady: {
    alignItems: 'center',
  },
  cameraReadyText: {
    color: '#9ca3af',
    marginTop: 16,
    marginBottom: 8,
  },
  cameraReadySubtext: {
    color: '#9ca3af',
    opacity: 0.8,
  },
  controls: {
    flexDirection: 'row',
    gap: 12,
  },
  controlButton: {
    flex: 1,
    borderRadius: 8,
  },
  resetButton: {
    borderRadius: 8,
  },
  summaryStats: {
    marginBottom: 16,
  },
  summaryLabel: {
    marginBottom: 8,
    fontWeight: '600',
  },
  progressBar: {
    height: 8,
    borderRadius: 4,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  statItem: {
    alignItems: 'center',
    backgroundColor: '#f9fafb',
    padding: 16,
    borderRadius: 8,
    flex: 1,
    marginHorizontal: 4,
  },
  statNumber: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    opacity: 0.7,
  },
  studentsList: {
    gap: 12,
    marginTop: 8,
  },
  studentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
  },
  studentInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  studentAvatar: {
    marginRight: 12,
  },
  studentDetails: {
    flex: 1,
  },
  studentName: {
    fontWeight: '600',
    marginBottom: 2,
  },
  studentId: {
    opacity: 0.7,
    marginBottom: 2,
  },
  timestamp: {
    opacity: 0.6,
    fontSize: 11,
  },
  studentStatus: {
    alignItems: 'center',
  },
  statusContainer: {
    alignItems: 'center',
    gap: 4,
  },
  confidence: {
    fontSize: 11,
    color: '#10b981',
    fontWeight: '600',
  },
  manualButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  manualButton: {
    minWidth: 32,
    height: 32,
  },
});