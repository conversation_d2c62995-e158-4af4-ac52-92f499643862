import React, { useState } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, List, Checkbox, Button, FAB, Portal, Dialog, TextInput } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';

interface Student {
  id: string;
  name: string;
  present: boolean;
}

export default function AttendanceScreen() {
  const [students, setStudents] = useState<Student[]>([
    { id: '1', name: '<PERSON>', present: false },
    { id: '2', name: '<PERSON>', present: false },
    { id: '3', name: '<PERSON>', present: false },
  ]);
  const [visible, setVisible] = useState(false);
  const [newStudentName, setNewStudentName] = useState('');

  const toggleAttendance = (id: string) => {
    setStudents(students.map(student =>
      student.id === id ? { ...student, present: !student.present } : student
    ));
  };

  const addStudent = () => {
    if (newStudentName.trim()) {
      const newStudent: Student = {
        id: Date.now().toString(),
        name: newStudentName.trim(),
        present: false,
      };
      setStudents([...students, newStudent]);
      setNewStudentName('');
      setVisible(false);
    }
  };

  const presentCount = students.filter(student => student.present).length;
  const absentCount = students.length - presentCount;

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text variant="titleLarge" style={styles.title}>Class Attendance</Text>
        <View style={styles.stats}>
          <Text variant="bodyLarge">Present: {presentCount}</Text>
          <Text variant="bodyLarge">Absent: {absentCount}</Text>
        </View>
      </View>

      <ScrollView style={styles.list}>
        {students.map((student) => (
          <List.Item
            key={student.id}
            title={student.name}
            right={() => (
              <Checkbox
                status={student.present ? 'checked' : 'unchecked'}
                onPress={() => toggleAttendance(student.id)}
              />
            )}
          />
        ))}
      </ScrollView>

      <Portal>
        <Dialog visible={visible} onDismiss={() => setVisible(false)}>
          <Dialog.Title>Add New Student</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label="Student Name"
              value={newStudentName}
              onChangeText={setNewStudentName}
              mode="outlined"
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setVisible(false)}>Cancel</Button>
            <Button onPress={addStudent}>Add</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>

      <FAB
        icon="plus"
        style={styles.fab}
        onPress={() => setVisible(true)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  stats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  list: {
    flex: 1,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
}); 