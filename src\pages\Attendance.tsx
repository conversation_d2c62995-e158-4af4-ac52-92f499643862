
import React, { useState } from 'react';
import { Camera, Play, Square, Users, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/hooks/use-toast';
import Navbar from '@/components/Navbar';

interface AttendanceRecord {
  id: string;
  name: string;
  studentId: string;
  status: 'present' | 'absent' | 'pending';
  timestamp?: string;
  confidence?: number;
}

const Attendance = () => {
  const [selectedClass, setSelectedClass] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([
    { id: '1', name: '<PERSON>', studentId: 'CS2024001', status: 'pending' },
    { id: '2', name: 'Bob <PERSON>', studentId: 'CS2024002', status: 'pending' },
    { id: '3', name: 'Carol Williams', studentId: 'CS2024003', status: 'pending' },
    { id: '4', name: 'David Brown', studentId: 'CS2024004', status: 'pending' },
  ]);

  const classes = [
    'Computer Science 101',
    'Data Structures', 
    'Machine Learning',
    'Web Development'
  ];

  const presentCount = attendanceRecords.filter(r => r.status === 'present').length;
  const totalStudents = attendanceRecords.length;
  const attendancePercentage = totalStudents > 0 ? (presentCount / totalStudents) * 100 : 0;

  const startRecognition = () => {
    if (!selectedClass) {
      toast({
        title: "Please select a class",
        description: "You need to select a class before starting attendance.",
        variant: "destructive",
      });
      return;
    }

    setIsRecording(true);
    toast({
      title: "Facial recognition started",
      description: "Students can now stand in front of the camera for attendance.",
    });

    // Simulate facial recognition process
    let recognizedCount = 0;
    const interval = setInterval(() => {
      const pendingStudents = attendanceRecords.filter(r => r.status === 'pending');
      if (pendingStudents.length > 0 && recognizedCount < 3) {
        const randomStudent = pendingStudents[Math.floor(Math.random() * pendingStudents.length)];
        setAttendanceRecords(prev => prev.map(record => 
          record.id === randomStudent.id 
            ? { 
                ...record, 
                status: 'present', 
                timestamp: new Date().toLocaleTimeString(),
                confidence: 92 + Math.floor(Math.random() * 8)
              }
            : record
        ));
        recognizedCount++;
        
        toast({
          title: "Student recognized",
          description: `${randomStudent.name} marked as present.`,
        });
      }
    }, 2000);

    // Auto-stop after 10 seconds for demo
    setTimeout(() => {
      clearInterval(interval);
      setIsRecording(false);
    }, 10000);
  };

  const stopRecognition = () => {
    setIsRecording(false);
    toast({
      title: "Attendance session ended",
      description: "Facial recognition has been stopped.",
    });
  };

  const markManually = (id: string, status: 'present' | 'absent') => {
    setAttendanceRecords(prev => prev.map(record => 
      record.id === id 
        ? { 
            ...record, 
            status, 
            timestamp: new Date().toLocaleTimeString()
          }
        : record
    ));
    
    const student = attendanceRecords.find(r => r.id === id);
    toast({
      title: `${student?.name} marked as ${status}`,
      description: "Manual attendance update completed.",
    });
  };

  const resetAttendance = () => {
    setAttendanceRecords(prev => prev.map(record => ({
      ...record,
      status: 'pending' as const,
      timestamp: undefined,
      confidence: undefined
    })));
    toast({
      title: "Attendance reset",
      description: "All attendance records have been cleared.",
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Attendance</h1>
          <p className="text-gray-600 mt-2">
            Use AI-powered facial recognition to mark student attendance
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Camera and Controls */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Camera className="w-5 h-5" />
                  <span>Facial Recognition Camera</span>
                </CardTitle>
                <CardDescription>
                  Students should stand in front of the camera one at a time
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {/* Class Selection */}
                  <div>
                    <label className="block text-sm font-medium mb-2">Select Class</label>
                    <Select value={selectedClass} onValueChange={setSelectedClass}>
                      <SelectTrigger>
                        <SelectValue placeholder="Choose a class for attendance" />
                      </SelectTrigger>
                      <SelectContent>
                        {classes.map((className) => (
                          <SelectItem key={className} value={className}>
                            {className}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Camera View (Simulated) */}
                  <div className="relative bg-gray-900 rounded-lg overflow-hidden">
                    <div className="aspect-video flex items-center justify-center">
                      {isRecording ? (
                        <div className="text-center text-white">
                          <div className="w-16 h-16 bg-red-500 rounded-full mx-auto mb-4 flex items-center justify-center animate-pulse">
                            <Camera className="w-8 h-8" />
                          </div>
                          <p className="text-lg font-medium">Facial Recognition Active</p>
                          <p className="text-sm opacity-75">Looking for faces...</p>
                        </div>
                      ) : (
                        <div className="text-center text-gray-300">
                          <Camera className="w-16 h-16 mx-auto mb-4" />
                          <p className="text-lg font-medium">Camera Ready</p>
                          <p className="text-sm">Click "Start Recognition" to begin</p>
                        </div>
                      )}
                    </div>
                    
                    {isRecording && (
                      <div className="absolute top-4 left-4">
                        <div className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-2">
                          <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                          <span>RECORDING</span>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Controls */}
                  <div className="flex space-x-4">
                    {!isRecording ? (
                      <Button 
                        onClick={startRecognition}
                        className="flex-1 bg-green-600 hover:bg-green-700"
                        disabled={!selectedClass}
                      >
                        <Play className="w-4 h-4 mr-2" />
                        Start Recognition
                      </Button>
                    ) : (
                      <Button 
                        onClick={stopRecognition}
                        variant="destructive"
                        className="flex-1"
                      >
                        <Square className="w-4 h-4 mr-2" />
                        Stop Recognition
                      </Button>
                    )}
                    
                    <Button 
                      onClick={resetAttendance}
                      variant="outline"
                    >
                      <RefreshCw className="w-4 h-4 mr-2" />
                      Reset
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Attendance Summary */}
          <div>
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="w-5 h-5" />
                  <span>Session Summary</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Attendance Rate</span>
                      <span className="font-medium">{attendancePercentage.toFixed(1)}%</span>
                    </div>
                    <Progress value={attendancePercentage} className="h-2" />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-center">
                    <div className="bg-green-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{presentCount}</div>
                      <div className="text-sm text-green-600">Present</div>
                    </div>
                    <div className="bg-gray-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-gray-600">{totalStudents - presentCount}</div>
                      <div className="text-sm text-gray-600">Pending</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Student List */}
            <Card>
              <CardHeader>
                <CardTitle>Students</CardTitle>
                <CardDescription>
                  Real-time attendance status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {attendanceRecords.map((record) => (
                    <div key={record.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Avatar className="w-8 h-8">
                          <AvatarFallback className="text-xs">
                            {record.name.split(' ').map(n => n[0]).join('')}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium text-sm">{record.name}</div>
                          <div className="text-xs text-gray-500">{record.studentId}</div>
                          {record.timestamp && (
                            <div className="text-xs text-gray-400">{record.timestamp}</div>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2">
                        {record.status === 'present' && (
                          <div className="flex items-center space-x-1">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            {record.confidence && (
                              <span className="text-xs text-green-600">{record.confidence}%</span>
                            )}
                          </div>
                        )}
                        {record.status === 'absent' && (
                          <XCircle className="w-4 h-4 text-red-500" />
                        )}
                        {record.status === 'pending' && (
                          <div className="flex space-x-1">
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-6 px-2 text-xs"
                              onClick={() => markManually(record.id, 'present')}
                            >
                              ✓
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="h-6 px-2 text-xs"
                              onClick={() => markManually(record.id, 'absent')}
                            >
                              ✗
                            </Button>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Attendance;
