import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  FAB, 
  Portal, 
  Dialog, 
  TextInput, 
  useTheme,
  IconButton,
  Avatar,
  Searchbar
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface Student {
  id: string;
  name: string;
  email: string;
  studentId: string;
  class: string;
  attendanceRate: number;
  photo?: string;
}

export default function StudentsScreen() {
  const theme = useTheme();
  const [students, setStudents] = useState<Student[]>([
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      studentId: 'CS2024001',
      class: 'Computer Science 101',
      attendanceRate: 95
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      studentId: 'CS2024002',
      class: 'Data Structures',
      attendanceRate: 88
    },
    {
      id: '3',
      name: '<PERSON>',
      email: '<EMAIL>',
      studentId: 'CS2024003',
      class: 'Machine Learning',
      attendanceRate: 92
    },
    {
      id: '4',
      name: '<PERSON>',
      email: '<EMAIL>',
      studentId: 'CS2024004',
      class: 'Computer Science 101',
      attendanceRate: 78
    }
  ]);

  const [searchQuery, setSearchQuery] = useState('');
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    studentId: '',
    class: ''
  });

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    student.studentId.toLowerCase().includes(searchQuery.toLowerCase()) ||
    student.class.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const openDialog = (student?: Student) => {
    if (student) {
      setEditingStudent(student);
      setFormData({
        name: student.name,
        email: student.email,
        studentId: student.studentId,
        class: student.class
      });
    } else {
      setEditingStudent(null);
      setFormData({
        name: '',
        email: '',
        studentId: '',
        class: ''
      });
    }
    setIsDialogVisible(true);
  };

  const closeDialog = () => {
    setIsDialogVisible(false);
    setEditingStudent(null);
    setFormData({
      name: '',
      email: '',
      studentId: '',
      class: ''
    });
  };

  const handleSubmit = () => {
    if (!formData.name || !formData.email || !formData.studentId || !formData.class) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (editingStudent) {
      setStudents(students.map(student => 
        student.id === editingStudent.id 
          ? { ...student, ...formData }
          : student
      ));
      Alert.alert('Success', 'Student updated successfully');
    } else {
      const newStudent: Student = {
        id: Date.now().toString(),
        ...formData,
        attendanceRate: 0
      };
      setStudents([...students, newStudent]);
      Alert.alert('Success', 'Student added successfully');
    }
    
    closeDialog();
  };

  const handleDelete = (id: string) => {
    Alert.alert(
      'Remove Student',
      'Are you sure you want to remove this student?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Remove', 
          style: 'destructive',
          onPress: () => {
            setStudents(students.filter(student => student.id !== id));
            Alert.alert('Success', 'Student removed successfully');
          }
        }
      ]
    );
  };

  const getAttendanceColor = (rate: number) => {
    if (rate >= 90) return '#10b981';
    if (rate >= 80) return '#f59e0b';
    return '#ef4444';
  };

  const getAttendanceStatus = (rate: number) => {
    if (rate >= 90) return 'Excellent';
    if (rate >= 80) return 'Good';
    return 'Needs Attention';
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <View style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text variant="headlineMedium" style={styles.title}>
            Students
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            Manage student information and enrollment
          </Text>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder="Search students..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchbar}
          />
        </View>

        {/* Students List */}
        <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
          <View style={styles.studentsContainer}>
            {filteredStudents.map((student) => (
              <Card key={student.id} style={styles.studentCard}>
                <Card.Content style={styles.cardContent}>
                  <View style={styles.studentHeader}>
                    <View style={styles.studentInfo}>
                      <Avatar.Text 
                        size={50} 
                        label={student.name.split(' ').map(n => n[0]).join('')}
                        style={[styles.avatar, { backgroundColor: theme.colors.primary }]}
                        labelStyle={{ color: theme.colors.secondary }}
                      />
                      <View style={styles.studentDetails}>
                        <Text variant="titleMedium" style={styles.studentName}>
                          {student.name}
                        </Text>
                        <Text variant="bodyMedium" style={styles.studentId}>
                          ID: {student.studentId}
                        </Text>
                        <Text variant="bodySmall" style={styles.studentEmail}>
                          {student.email}
                        </Text>
                      </View>
                    </View>
                    <View style={styles.actions}>
                      <IconButton
                        icon="pencil"
                        size={20}
                        onPress={() => openDialog(student)}
                      />
                      <IconButton
                        icon="delete"
                        size={20}
                        onPress={() => handleDelete(student.id)}
                      />
                    </View>
                  </View>

                  <View style={styles.studentMeta}>
                    <View style={styles.metaRow}>
                      <MaterialCommunityIcons 
                        name="book-open-variant" 
                        size={16} 
                        color={theme.colors.primary} 
                      />
                      <Text variant="bodyMedium" style={styles.metaText}>
                        {student.class}
                      </Text>
                    </View>
                    
                    <View style={styles.attendanceRow}>
                      <MaterialCommunityIcons 
                        name="chart-line" 
                        size={16} 
                        color={getAttendanceColor(student.attendanceRate)} 
                      />
                      <Text 
                        variant="bodyMedium" 
                        style={[styles.attendanceText, { color: getAttendanceColor(student.attendanceRate) }]}
                      >
                        {student.attendanceRate}% - {getAttendanceStatus(student.attendanceRate)}
                      </Text>
                    </View>
                  </View>
                </Card.Content>
              </Card>
            ))}
          </View>
        </ScrollView>
      </View>

      {/* Add Student FAB */}
      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => openDialog()}
      />

      {/* Add/Edit Student Dialog */}
      <Portal>
        <Dialog visible={isDialogVisible} onDismiss={closeDialog}>
          <Dialog.Title>
            {editingStudent ? 'Edit Student' : 'Add New Student'}
          </Dialog.Title>
          <Dialog.Content>
            <View style={styles.formContainer}>
              <TextInput
                label="Full Name"
                value={formData.name}
                onChangeText={(text) => setFormData({ ...formData, name: text })}
                mode="outlined"
                style={styles.input}
              />
              
              <TextInput
                label="Email Address"
                value={formData.email}
                onChangeText={(text) => setFormData({ ...formData, email: text })}
                mode="outlined"
                keyboardType="email-address"
                style={styles.input}
              />
              
              <TextInput
                label="Student ID"
                value={formData.studentId}
                onChangeText={(text) => setFormData({ ...formData, studentId: text })}
                mode="outlined"
                style={styles.input}
              />
              
              <TextInput
                label="Class"
                value={formData.class}
                onChangeText={(text) => setFormData({ ...formData, class: text })}
                mode="outlined"
                style={styles.input}
              />
            </View>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={closeDialog}>Cancel</Button>
            <Button onPress={handleSubmit} mode="contained">
              {editingStudent ? 'Update' : 'Add'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 16,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    opacity: 0.7,
  },
  searchContainer: {
    paddingHorizontal: 20,
    paddingBottom: 16,
  },
  searchbar: {
    elevation: 2,
  },
  scrollView: {
    flex: 1,
  },
  studentsContainer: {
    padding: 20,
    paddingTop: 0,
    gap: 16,
  },
  studentCard: {
    elevation: 2,
  },
  cardContent: {
    padding: 16,
  },
  studentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  studentInfo: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'center',
  },
  avatar: {
    marginRight: 12,
  },
  studentDetails: {
    flex: 1,
  },
  studentName: {
    fontWeight: 'bold',
    marginBottom: 2,
  },
  studentId: {
    opacity: 0.7,
    marginBottom: 2,
  },
  studentEmail: {
    opacity: 0.6,
  },
  actions: {
    flexDirection: 'row',
  },
  studentMeta: {
    gap: 8,
  },
  metaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  metaText: {
    opacity: 0.8,
  },
  attendanceRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  attendanceText: {
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  formContainer: {
    gap: 16,
  },
  input: {
    backgroundColor: 'transparent',
  },
});
