
import React from 'react';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

const AttendanceChart = () => {
  const data = [
    { day: 'Mon', attendance: 92 },
    { day: 'Tue', attendance: 88 },
    { day: 'Wed', attendance: 95 },
    { day: 'Thu', attendance: 90 },
    { day: 'Fri', attendance: 87 },
    { day: 'Sat', attendance: 94 },
    { day: 'Sun', attendance: 89 },
  ];

  return (
    <div className="h-64">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="day" />
          <YAxis domain={[80, 100]} />
          <Tooltip 
            formatter={(value) => [`${value}%`, 'Attendance Rate']}
            labelFormatter={(label) => `Day: ${label}`}
          />
          <Line 
            type="monotone" 
            dataKey="attendance" 
            stroke="hsl(var(--ustp-navy))" 
            strokeWidth={3}
            dot={{ fill: 'hsl(var(--ustp-navy))', strokeWidth: 2, r: 6 }}
            activeDot={{ r: 8, fill: 'hsl(var(--ustp-gold))' }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default AttendanceChart;
