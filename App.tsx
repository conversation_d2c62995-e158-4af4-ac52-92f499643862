import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaperProvider, MD3LightTheme } from 'react-native-paper';
import React, { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import your screens here
import LoginScreen from './src/screens/LoginScreen';
import DashboardScreen from './src/screens/DashboardScreen';
import ClassesScreen from './src/screens/ClassesScreen';
import StudentsScreen from './src/screens/StudentsScreen';
import AttendanceScreen from './src/screens/AttendanceScreen';
import ReportsScreen from './src/screens/ReportsScreen';
import ProfileScreen from './src/screens/ProfileScreen';

const Stack = createNativeStackNavigator();
const queryClient = new QueryClient();

// USTP Theme
const theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: '#1e293b', // USTP Navy
    secondary: '#fbbf24', // USTP Gold
    surface: '#ffffff',
    background: '#f9fafb',
    onPrimary: '#fbbf24',
    onSecondary: '#1e293b',
  },
};

export default function App() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  useEffect(() => {
    checkAuthStatus();

    // Set up a listener for auth changes
    const interval = setInterval(checkAuthStatus, 1000);
    return () => clearInterval(interval);
  }, []);

  const checkAuthStatus = async () => {
    try {
      const authStatus = await AsyncStorage.getItem('isAuthenticated');
      setIsAuthenticated(authStatus === 'true');
    } catch (error) {
      setIsAuthenticated(false);
    }
  };

  if (isAuthenticated === null) {
    return null; // Loading state
  }

  return (
    <QueryClientProvider client={queryClient}>
      <PaperProvider theme={theme}>
        <SafeAreaProvider>
          <NavigationContainer>
            <Stack.Navigator
              screenOptions={{
                headerStyle: {
                  backgroundColor: '#1e293b',
                },
                headerTintColor: '#fbbf24',
                headerTitleStyle: {
                  fontWeight: 'bold',
                },
              }}
            >
              {!isAuthenticated ? (
                <Stack.Screen
                  name="Login"
                  component={LoginScreen}
                  options={{ headerShown: false }}
                />
              ) : (
                <>
                  <Stack.Screen
                    name="Dashboard"
                    component={DashboardScreen}
                    options={{ title: 'Dashboard' }}
                  />
                  <Stack.Screen
                    name="Classes"
                    component={ClassesScreen}
                    options={{ title: 'Classes' }}
                  />
                  <Stack.Screen
                    name="Students"
                    component={StudentsScreen}
                    options={{ title: 'Students' }}
                  />
                  <Stack.Screen
                    name="Attendance"
                    component={AttendanceScreen}
                    options={{ title: 'Attendance' }}
                  />
                  <Stack.Screen
                    name="Reports"
                    component={ReportsScreen}
                    options={{ title: 'Reports' }}
                  />
                  <Stack.Screen
                    name="Profile"
                    component={ProfileScreen}
                    options={{ title: 'Profile' }}
                  />
                </>
              )}
            </Stack.Navigator>
          </NavigationContainer>
          <StatusBar style="light" />
        </SafeAreaProvider>
      </PaperProvider>
    </QueryClientProvider>
  );
}