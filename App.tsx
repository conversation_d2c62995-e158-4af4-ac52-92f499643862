import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PaperProvider, MD3LightTheme } from 'react-native-paper';
import React, { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { View, Text, StyleSheet } from 'react-native';
import { MaterialCommunityIcons } from '@expo/vector-icons';

// Import your screens here
import LoginScreen from './src/screens/LoginScreen';
import DashboardScreen from './src/screens/DashboardScreen';
import AttendanceScreen from './src/screens/AttendanceScreen';
import ProfileScreen from './src/screens/ProfileScreen';

const Stack = createNativeStackNavigator();
const queryClient = new QueryClient();

// USTP Theme
const theme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: '#002154', // USTP Navy Blue
    secondary: '#FFD700', // USTP Gold
    surface: '#ffffff',
    background: '#f9fafb',
    onPrimary: '#FFD700',
    onSecondary: '#002154',
  },
};

// Splash Screen Component
function SplashScreen() {
  return (
    <View style={splashStyles.container}>
      <View style={splashStyles.logoContainer}>
        <View style={[splashStyles.logoCircle, { backgroundColor: theme.colors.secondary }]}>
          <MaterialCommunityIcons
            name="school"
            size={60}
            color={theme.colors.primary}
          />
        </View>
        <Text style={[splashStyles.appTitle, { color: theme.colors.secondary }]}>
          AttendWise
        </Text>
        <Text style={[splashStyles.subtitle, { color: theme.colors.onPrimary }]}>
          Smart Classroom Management
        </Text>
      </View>
      <View style={splashStyles.loadingContainer}>
        <Text style={[splashStyles.loadingText, { color: theme.colors.onPrimary }]}>
          Loading...
        </Text>
      </View>
    </View>
  );
}

export default function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    // Show splash screen for 2 seconds
    setTimeout(async () => {
      await checkAuthStatus();
      setIsLoading(false);
    }, 2000);
  };

  const checkAuthStatus = async () => {
    try {
      const authStatus = await AsyncStorage.getItem('isAuthenticated');
      setIsAuthenticated(authStatus === 'true');
    } catch (error) {
      setIsAuthenticated(false);
    }
  };

  // Show splash screen while loading
  if (isLoading) {
    return (
      <PaperProvider theme={theme}>
        <SafeAreaProvider>
          <SplashScreen />
          <StatusBar style="light" />
        </SafeAreaProvider>
      </PaperProvider>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <PaperProvider theme={theme}>
        <SafeAreaProvider>
          <NavigationContainer>
            <Stack.Navigator
              screenOptions={{
                headerStyle: {
                  backgroundColor: theme.colors.primary,
                },
                headerTintColor: theme.colors.secondary,
                headerTitleStyle: {
                  fontWeight: 'bold',
                },
              }}
            >
              {!isAuthenticated ? (
                <Stack.Screen
                  name="Login"
                  component={LoginScreen}
                  options={{ headerShown: false }}
                />
              ) : (
                <>
                  <Stack.Screen
                    name="Dashboard"
                    component={DashboardScreen}
                    options={{ title: 'Dashboard' }}
                  />
                  <Stack.Screen
                    name="Attendance"
                    component={AttendanceScreen}
                    options={{ title: 'Attendance' }}
                  />
                  <Stack.Screen
                    name="Profile"
                    component={ProfileScreen}
                    options={{ title: 'Profile' }}
                  />
                </>
              )}
            </Stack.Navigator>
          </NavigationContainer>
          <StatusBar style="light" />
        </SafeAreaProvider>
      </PaperProvider>
    </QueryClientProvider>
  );
}

// Splash Screen Styles
const splashStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#002154',
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoContainer: {
    alignItems: 'center',
    marginBottom: 50,
  },
  logoCircle: {
    width: 120,
    height: 120,
    borderRadius: 60,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 24,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
  },
  appTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    opacity: 0.8,
    textAlign: 'center',
  },
  loadingContainer: {
    position: 'absolute',
    bottom: 100,
  },
  loadingText: {
    fontSize: 16,
    opacity: 0.7,
  },
});