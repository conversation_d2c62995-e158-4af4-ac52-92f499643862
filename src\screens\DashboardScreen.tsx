import React from 'react';
import { View, StyleSheet, ScrollView, Dimensions } from 'react-native';
import { Text, Card, Button, useTheme } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { LineChart } from 'react-native-chart-kit';

const { width } = Dimensions.get('window');

export default function DashboardScreen() {
  const theme = useTheme();

  const stats = [
    {
      title: 'Total Students',
      value: '156',
      icon: 'account-group',
      color: theme.colors.primary,
      change: '+12 this month'
    },
    {
      title: 'Active Classes',
      value: '8',
      icon: 'book-open-variant',
      color: theme.colors.secondary,
      change: '3 today'
    },
    {
      title: 'Attendance Rate',
      value: '94.2%',
      icon: 'trending-up',
      color: '#8b5cf6',
      change: '+2.1% vs last week'
    },
    {
      title: 'Present Today',
      value: '142',
      icon: 'account-check',
      color: '#f97316',
      change: '91% of total'
    }
  ];

  const recentClasses = [
    { name: 'Computer Science 101', time: '9:00 AM', students: 28, present: 26 },
    { name: 'Data Structures', time: '11:00 AM', students: 24, present: 22 },
    { name: 'Machine Learning', time: '2:00 PM', students: 32, present: 30 },
    { name: 'Web Development', time: '4:00 PM', students: 22, present: 20 }
  ];

  const chartData = {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],
    datasets: [
      {
        data: [92, 88, 95, 89, 94],
        strokeWidth: 3,
        color: (opacity = 1) => `rgba(30, 41, 59, ${opacity})`,
      }
    ]
  };

  const chartConfig = {
    backgroundColor: theme.colors.surface,
    backgroundGradientFrom: theme.colors.surface,
    backgroundGradientTo: theme.colors.surface,
    decimalPlaces: 0,
    color: (opacity = 1) => `rgba(30, 41, 59, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(107, 114, 128, ${opacity})`,
    style: {
      borderRadius: 16
    },
    propsForDots: {
      r: '6',
      strokeWidth: '2',
      stroke: theme.colors.secondary
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text variant="headlineMedium" style={styles.welcomeText}>
            Welcome back, Dr. Sarah!
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            Here's what's happening with your classes today.
          </Text>
        </View>

        {/* Stats Grid */}
        <View style={styles.statsGrid}>
          {stats.map((stat, index) => (
            <Card key={index} style={styles.statCard}>
              <Card.Content style={styles.statContent}>
                <View style={styles.statHeader}>
                  <View>
                    <Text variant="bodySmall" style={styles.statTitle}>{stat.title}</Text>
                    <Text variant="headlineSmall" style={styles.statValue}>{stat.value}</Text>
                    <Text variant="bodySmall" style={styles.statChange}>{stat.change}</Text>
                  </View>
                  <View style={[styles.iconContainer, { backgroundColor: stat.color }]}>
                    <MaterialCommunityIcons 
                      name={stat.icon as any} 
                      size={24} 
                      color={stat.color === theme.colors.secondary ? theme.colors.primary : 'white'} 
                    />
                  </View>
                </View>
              </Card.Content>
            </Card>
          ))}
        </View>

        {/* Quick Actions */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.cardTitle}>Quick Actions</Text>
            <Text variant="bodyMedium" style={styles.cardSubtitle}>
              Common tasks to manage your classes
            </Text>
            
            <View style={styles.actionsContainer}>
              <Button 
                mode="contained"
                icon="camera"
                style={[styles.actionButton, { backgroundColor: theme.colors.primary }]}
                contentStyle={styles.actionButtonContent}
              >
                Start Facial Recognition
              </Button>
              
              <Button 
                mode="outlined"
                icon="book-open-variant"
                style={styles.actionButton}
                contentStyle={styles.actionButtonContent}
              >
                Manage Classes
              </Button>
              
              <Button 
                mode="outlined"
                icon="account-group"
                style={styles.actionButton}
                contentStyle={styles.actionButtonContent}
              >
                Manage Students
              </Button>
            </View>
          </Card.Content>
        </Card>

        {/* Today's Classes */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.cardTitle}>Today's Classes</Text>
            <Text variant="bodyMedium" style={styles.cardSubtitle}>
              Overview of your scheduled classes
            </Text>
            
            <View style={styles.classesContainer}>
              {recentClasses.map((cls, index) => (
                <View key={index} style={styles.classItem}>
                  <View style={styles.classInfo}>
                    <Text variant="titleMedium" style={styles.className}>{cls.name}</Text>
                    <Text variant="bodyMedium" style={styles.classTime}>{cls.time}</Text>
                  </View>
                  <View style={styles.classStats}>
                    <Text variant="bodyMedium" style={styles.attendance}>
                      {cls.present}/{cls.students} present
                    </Text>
                    <Text variant="bodySmall" style={styles.percentage}>
                      {Math.round((cls.present / cls.students) * 100)}% attendance
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </Card.Content>
        </Card>

        {/* Attendance Trends */}
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleLarge" style={styles.cardTitle}>Attendance Trends</Text>
            <Text variant="bodyMedium" style={styles.cardSubtitle}>
              Weekly attendance patterns
            </Text>
            
            <View style={styles.chartContainer}>
              <LineChart
                data={chartData}
                width={width - 80}
                height={200}
                chartConfig={chartConfig}
                bezier
                style={styles.chart}
              />
            </View>
          </Card.Content>
        </Card>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 16,
  },
  welcomeText: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    opacity: 0.7,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: 20,
    gap: 12,
  },
  statCard: {
    width: (width - 52) / 2,
    elevation: 2,
  },
  statContent: {
    padding: 16,
  },
  statHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  statTitle: {
    opacity: 0.7,
    marginBottom: 4,
  },
  statValue: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statChange: {
    color: '#10b981',
    fontSize: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    margin: 20,
    marginTop: 16,
    elevation: 2,
  },
  cardTitle: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  cardSubtitle: {
    opacity: 0.7,
    marginBottom: 20,
  },
  actionsContainer: {
    gap: 12,
  },
  actionButton: {
    borderRadius: 8,
  },
  actionButtonContent: {
    paddingVertical: 8,
  },
  classesContainer: {
    gap: 12,
  },
  classItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f9fafb',
    borderRadius: 8,
  },
  classInfo: {
    flex: 1,
  },
  className: {
    fontWeight: '600',
    marginBottom: 4,
  },
  classTime: {
    opacity: 0.7,
  },
  classStats: {
    alignItems: 'flex-end',
  },
  attendance: {
    fontWeight: '600',
    marginBottom: 2,
  },
  percentage: {
    opacity: 0.7,
  },
  chartContainer: {
    alignItems: 'center',
    marginTop: 8,
  },
  chart: {
    borderRadius: 16,
  },
});
