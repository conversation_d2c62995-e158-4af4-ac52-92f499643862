import * as SQLite from 'expo-sqlite/legacy';

// Database interface types
export interface User {
  id: number;
  name: string;
  email: string;
  password: string;
  department: string;
  employeeId: string;
  role: 'teacher' | 'admin';
  createdAt: string;
}

export interface Class {
  id: number;
  name: string;
  code: string;
  schedule: string;
  room: string;
  teacherId: number;
  status: 'active' | 'inactive';
  createdAt: string;
}

export interface Student {
  id: number;
  name: string;
  studentId: string;
  email: string;
  course: string;
  year: string;
  status: 'active' | 'inactive';
  createdAt: string;
}

export interface ClassStudent {
  id: number;
  classId: number;
  studentId: number;
  enrolledAt: string;
}

export interface AttendanceRecord {
  id: number;
  classId: number;
  studentId: number;
  date: string;
  status: 'present' | 'absent' | 'late';
  confidence?: number;
  method: 'manual' | 'facial_recognition';
  createdAt: string;
}

class DatabaseService {
  private db: SQLite.WebSQLDatabase | null = null;

  async init(): Promise<void> {
    try {
      this.db = SQLite.openDatabase('attendwise.db');
      await this.createTables();
      await this.populateInitialData();
      console.log('Database initialized successfully');
    } catch (error) {
      console.error('Database initialization failed:', error);
      throw error;
    }
  }

  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    return new Promise((resolve, reject) => {
      this.db!.transaction(tx => {
        // Users table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            department TEXT NOT NULL,
            employeeId TEXT UNIQUE NOT NULL,
            role TEXT DEFAULT 'teacher',
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
          );
        `);

        // Classes table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS classes (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            code TEXT UNIQUE NOT NULL,
            schedule TEXT NOT NULL,
            room TEXT NOT NULL,
            teacherId INTEGER NOT NULL,
            status TEXT DEFAULT 'active',
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
          );
        `);

        // Students table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            studentId TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            course TEXT NOT NULL,
            year TEXT NOT NULL,
            status TEXT DEFAULT 'active',
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
          );
        `);

        // Class-Student relationship table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS class_students (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            classId INTEGER NOT NULL,
            studentId INTEGER NOT NULL,
            enrolledAt DATETIME DEFAULT CURRENT_TIMESTAMP
          );
        `);

        // Attendance records table
        tx.executeSql(`
          CREATE TABLE IF NOT EXISTS attendance_records (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            classId INTEGER NOT NULL,
            studentId INTEGER NOT NULL,
            date DATE NOT NULL,
            status TEXT NOT NULL,
            confidence INTEGER,
            method TEXT DEFAULT 'manual',
            createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
          );
        `);
      }, reject, () => {
        console.log('Database tables created successfully');
        resolve();
      });
    });
  }

  private async populateInitialData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // Check if data already exists
    const userCount = await this.db.getFirstAsync('SELECT COUNT(*) as count FROM users');
    if ((userCount as any)?.count > 0) {
      console.log('Initial data already exists, skipping population');
      return;
    }

    // Insert demo teacher/admin user
    await this.db.runAsync(`
      INSERT INTO users (name, email, password, department, employeeId, role) VALUES 
      ('Dr. John Smith', '<EMAIL>', 'demo123', 'Computer Science', 'EMP2024001', 'teacher'),
      ('Prof. Maria Garcia', '<EMAIL>', 'password123', 'Computer Science', 'EMP2024002', 'teacher'),
      ('Admin User', '<EMAIL>', 'admin123', 'Administration', 'EMP2024003', 'admin')
    `);

    // Insert demo classes
    await this.db.runAsync(`
      INSERT INTO classes (name, code, schedule, room, teacherId, status) VALUES 
      ('Computer Science 101', 'CS101', 'MWF 8:00-9:00 AM', 'Room 201', 1, 'active'),
      ('Data Structures', 'CS201', 'TTH 10:00-11:30 AM', 'Room 203', 1, 'active'),
      ('Database Systems', 'CS301', 'MWF 2:00-3:00 PM', 'Room 205', 2, 'active'),
      ('Web Development', 'CS401', 'TTH 1:00-2:30 PM', 'Room 207', 2, 'active'),
      ('Software Engineering', 'CS501', 'MWF 10:00-11:00 AM', 'Room 209', 1, 'inactive')
    `);

    // Insert demo students
    await this.db.runAsync(`
      INSERT INTO students (name, studentId, email, course, year, status) VALUES 
      ('Alice Johnson', 'CS2024001', '<EMAIL>', 'Computer Science', '3rd Year', 'active'),
      ('Bob Smith', 'CS2024002', '<EMAIL>', 'Computer Science', '2nd Year', 'active'),
      ('Carol Davis', 'CS2024003', '<EMAIL>', 'Information Technology', '4th Year', 'active'),
      ('David Brown', 'CS2024004', '<EMAIL>', 'Computer Science', '1st Year', 'active'),
      ('Eva Wilson', 'CS2024005', '<EMAIL>', 'Computer Science', '3rd Year', 'active'),
      ('Frank Miller', 'CS2024006', '<EMAIL>', 'Information Technology', '2nd Year', 'active'),
      ('Grace Lee', 'CS2024007', '<EMAIL>', 'Computer Science', '4th Year', 'active'),
      ('Henry Taylor', 'CS2024008', '<EMAIL>', 'Computer Science', '1st Year', 'active'),
      ('Ivy Chen', 'CS2024009', '<EMAIL>', 'Information Technology', '3rd Year', 'active'),
      ('Jack Anderson', 'CS2024010', '<EMAIL>', 'Computer Science', '2nd Year', 'active')
    `);

    // Enroll students in classes
    await this.db.runAsync(`
      INSERT INTO class_students (classId, studentId) VALUES 
      (1, 1), (1, 2), (1, 4), (1, 8), (1, 10),
      (2, 1), (2, 3), (2, 5), (2, 7), (2, 9),
      (3, 2), (3, 3), (3, 6), (3, 7), (3, 10),
      (4, 1), (4, 4), (4, 5), (4, 8), (4, 9),
      (5, 3), (5, 5), (5, 6), (5, 7), (5, 9)
    `);

    // Insert sample attendance records for the past week
    const today = new Date();
    const dates = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      dates.push(date.toISOString().split('T')[0]);
    }

    // Generate attendance records
    for (const date of dates) {
      // CS101 attendance
      await this.db.runAsync(`
        INSERT INTO attendance_records (classId, studentId, date, status, confidence, method) VALUES 
        (1, 1, '${date}', 'present', 95, 'facial_recognition'),
        (1, 2, '${date}', 'present', 88, 'facial_recognition'),
        (1, 4, '${date}', 'absent', NULL, 'manual'),
        (1, 8, '${date}', 'present', 92, 'facial_recognition'),
        (1, 10, '${date}', 'late', 85, 'facial_recognition')
      `);

      // CS201 attendance
      await this.db.runAsync(`
        INSERT INTO attendance_records (classId, studentId, date, status, confidence, method) VALUES 
        (2, 1, '${date}', 'present', 90, 'facial_recognition'),
        (2, 3, '${date}', 'present', 87, 'facial_recognition'),
        (2, 5, '${date}', 'present', 93, 'facial_recognition'),
        (2, 7, '${date}', 'absent', NULL, 'manual'),
        (2, 9, '${date}', 'present', 89, 'facial_recognition')
      `);
    }

    console.log('Initial data populated successfully');
  }

  // User operations
  async authenticateUser(email: string, password: string): Promise<User | null> {
    if (!this.db) throw new Error('Database not initialized');
    
    const user = await this.db.getFirstAsync(
      'SELECT * FROM users WHERE email = ? AND password = ?',
      [email, password]
    ) as User | null;
    
    return user;
  }

  async getUserById(id: number): Promise<User | null> {
    if (!this.db) throw new Error('Database not initialized');
    
    const user = await this.db.getFirstAsync(
      'SELECT * FROM users WHERE id = ?',
      [id]
    ) as User | null;
    
    return user;
  }

  // Class operations
  async getAllClasses(): Promise<Class[]> {
    if (!this.db) throw new Error('Database not initialized');
    
    const classes = await this.db.getAllAsync(
      'SELECT * FROM classes ORDER BY createdAt DESC'
    ) as Class[];
    
    return classes;
  }

  async getClassById(id: number): Promise<Class | null> {
    if (!this.db) throw new Error('Database not initialized');
    
    const classData = await this.db.getFirstAsync(
      'SELECT * FROM classes WHERE id = ?',
      [id]
    ) as Class | null;
    
    return classData;
  }

  async createClass(classData: Omit<Class, 'id' | 'createdAt'>): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');
    
    const result = await this.db.runAsync(
      'INSERT INTO classes (name, code, schedule, room, teacherId, status) VALUES (?, ?, ?, ?, ?, ?)',
      [classData.name, classData.code, classData.schedule, classData.room, classData.teacherId, classData.status]
    );
    
    return result.lastInsertRowId;
  }

  async updateClass(id: number, classData: Partial<Omit<Class, 'id' | 'createdAt'>>): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');
    
    const fields = Object.keys(classData).map(key => `${key} = ?`).join(', ');
    const values = Object.values(classData);
    
    await this.db.runAsync(
      `UPDATE classes SET ${fields} WHERE id = ?`,
      [...values, id]
    );
  }

  async deleteClass(id: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync('DELETE FROM classes WHERE id = ?', [id]);
  }

  // Student operations
  async getAllStudents(): Promise<Student[]> {
    if (!this.db) throw new Error('Database not initialized');

    const students = await this.db.getAllAsync(
      'SELECT * FROM students ORDER BY name ASC'
    ) as Student[];

    return students;
  }

  async getStudentById(id: number): Promise<Student | null> {
    if (!this.db) throw new Error('Database not initialized');

    const student = await this.db.getFirstAsync(
      'SELECT * FROM students WHERE id = ?',
      [id]
    ) as Student | null;

    return student;
  }

  async createStudent(studentData: Omit<Student, 'id' | 'createdAt'>): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.runAsync(
      'INSERT INTO students (name, studentId, email, course, year, status) VALUES (?, ?, ?, ?, ?, ?)',
      [studentData.name, studentData.studentId, studentData.email, studentData.course, studentData.year, studentData.status]
    );

    return result.lastInsertRowId;
  }

  async updateStudent(id: number, studentData: Partial<Omit<Student, 'id' | 'createdAt'>>): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const fields = Object.keys(studentData).map(key => `${key} = ?`).join(', ');
    const values = Object.values(studentData);

    await this.db.runAsync(
      `UPDATE students SET ${fields} WHERE id = ?`,
      [...values, id]
    );
  }

  async deleteStudent(id: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync('DELETE FROM students WHERE id = ?', [id]);
  }

  async getStudentsByClass(classId: number): Promise<Student[]> {
    if (!this.db) throw new Error('Database not initialized');

    const students = await this.db.getAllAsync(`
      SELECT s.* FROM students s
      JOIN class_students cs ON s.id = cs.studentId
      WHERE cs.classId = ?
      ORDER BY s.name ASC
    `, [classId]) as Student[];

    return students;
  }

  // Attendance operations
  async recordAttendance(attendanceData: Omit<AttendanceRecord, 'id' | 'createdAt'>): Promise<number> {
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.runAsync(`
      INSERT OR REPLACE INTO attendance_records
      (classId, studentId, date, status, confidence, method)
      VALUES (?, ?, ?, ?, ?, ?)
    `, [
      attendanceData.classId,
      attendanceData.studentId,
      attendanceData.date,
      attendanceData.status,
      attendanceData.confidence,
      attendanceData.method
    ]);

    return result.lastInsertRowId;
  }

  async getAttendanceByClass(classId: number, date?: string): Promise<AttendanceRecord[]> {
    if (!this.db) throw new Error('Database not initialized');

    let query = 'SELECT * FROM attendance_records WHERE classId = ?';
    const params: any[] = [classId];

    if (date) {
      query += ' AND date = ?';
      params.push(date);
    }

    query += ' ORDER BY createdAt DESC';

    const records = await this.db.getAllAsync(query, params) as AttendanceRecord[];
    return records;
  }

  async getAttendanceByStudent(studentId: number, startDate?: string, endDate?: string): Promise<AttendanceRecord[]> {
    if (!this.db) throw new Error('Database not initialized');

    let query = 'SELECT * FROM attendance_records WHERE studentId = ?';
    const params: any[] = [studentId];

    if (startDate) {
      query += ' AND date >= ?';
      params.push(startDate);
    }

    if (endDate) {
      query += ' AND date <= ?';
      params.push(endDate);
    }

    query += ' ORDER BY date DESC';

    const records = await this.db.getAllAsync(query, params) as AttendanceRecord[];
    return records;
  }

  async getAttendanceStats(classId?: number, startDate?: string, endDate?: string): Promise<any> {
    if (!this.db) throw new Error('Database not initialized');

    let query = `
      SELECT
        COUNT(*) as totalRecords,
        SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) as presentCount,
        SUM(CASE WHEN status = 'absent' THEN 1 ELSE 0 END) as absentCount,
        SUM(CASE WHEN status = 'late' THEN 1 ELSE 0 END) as lateCount,
        ROUND(
          (SUM(CASE WHEN status = 'present' THEN 1 ELSE 0 END) * 100.0 / COUNT(*)), 2
        ) as attendanceRate
      FROM attendance_records
      WHERE 1=1
    `;

    const params: any[] = [];

    if (classId) {
      query += ' AND classId = ?';
      params.push(classId);
    }

    if (startDate) {
      query += ' AND date >= ?';
      params.push(startDate);
    }

    if (endDate) {
      query += ' AND date <= ?';
      params.push(endDate);
    }

    const stats = await this.db.getFirstAsync(query, params);
    return stats;
  }

  // Enrollment operations
  async enrollStudentInClass(classId: number, studentId: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      'INSERT OR IGNORE INTO class_students (classId, studentId) VALUES (?, ?)',
      [classId, studentId]
    );
  }

  async unenrollStudentFromClass(classId: number, studentId: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.runAsync(
      'DELETE FROM class_students WHERE classId = ? AND studentId = ?',
      [classId, studentId]
    );
  }

  // Utility methods
  async clearAllData(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    await this.db.execAsync(`
      DELETE FROM attendance_records;
      DELETE FROM class_students;
      DELETE FROM students;
      DELETE FROM classes;
      DELETE FROM users;
    `);
  }

  async closeDatabase(): Promise<void> {
    if (this.db) {
      await this.db.closeAsync();
      this.db = null;
    }
  }
}

export const databaseService = new DatabaseService();
