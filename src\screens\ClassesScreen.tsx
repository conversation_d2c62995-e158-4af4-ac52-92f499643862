import React, { useState } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { 
  Text, 
  Card, 
  Button, 
  FAB, 
  Portal, 
  Dialog, 
  TextInput, 
  useTheme,
  IconButton 
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialCommunityIcons } from '@expo/vector-icons';

interface Class {
  id: string;
  name: string;
  subject: string;
  schedule: string;
  time: string;
  room: string;
  students: number;
}

export default function ClassesScreen() {
  const theme = useTheme();
  const [classes, setClasses] = useState<Class[]>([
    {
      id: '1',
      name: 'Computer Science 101',
      subject: 'Introduction to Programming',
      schedule: 'Monday, Wednesday, Friday',
      time: '9:00 AM - 10:30 AM',
      room: 'Room 201',
      students: 28
    },
    {
      id: '2',
      name: 'Data Structures',
      subject: 'Advanced Programming',
      schedule: 'Tuesday, Thursday',
      time: '11:00 AM - 12:30 PM',
      room: 'Room 305',
      students: 24
    },
    {
      id: '3',
      name: 'Machine Learning',
      subject: 'Artificial Intelligence',
      schedule: 'Monday, Wednesday',
      time: '2:00 PM - 3:30 PM',
      room: 'Lab 101',
      students: 32
    }
  ]);

  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [editingClass, setEditingClass] = useState<Class | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    subject: '',
    schedule: '',
    time: '',
    room: ''
  });

  const openDialog = (cls?: Class) => {
    if (cls) {
      setEditingClass(cls);
      setFormData({
        name: cls.name,
        subject: cls.subject,
        schedule: cls.schedule,
        time: cls.time,
        room: cls.room
      });
    } else {
      setEditingClass(null);
      setFormData({
        name: '',
        subject: '',
        schedule: '',
        time: '',
        room: ''
      });
    }
    setIsDialogVisible(true);
  };

  const closeDialog = () => {
    setIsDialogVisible(false);
    setEditingClass(null);
    setFormData({
      name: '',
      subject: '',
      schedule: '',
      time: '',
      room: ''
    });
  };

  const handleSubmit = () => {
    if (!formData.name || !formData.subject || !formData.schedule || !formData.time || !formData.room) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    if (editingClass) {
      setClasses(classes.map(cls => 
        cls.id === editingClass.id 
          ? { ...cls, ...formData }
          : cls
      ));
      Alert.alert('Success', 'Class updated successfully');
    } else {
      const newClass: Class = {
        id: Date.now().toString(),
        ...formData,
        students: 0
      };
      setClasses([...classes, newClass]);
      Alert.alert('Success', 'Class created successfully');
    }
    
    closeDialog();
  };

  const handleDelete = (id: string) => {
    Alert.alert(
      'Delete Class',
      'Are you sure you want to delete this class?',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: () => {
            setClasses(classes.filter(cls => cls.id !== id));
            Alert.alert('Success', 'Class deleted successfully');
          }
        }
      ]
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text variant="headlineMedium" style={styles.title}>
            Classes
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            Manage your class schedules and information
          </Text>
        </View>

        {/* Classes List */}
        <View style={styles.classesContainer}>
          {classes.map((cls) => (
            <Card key={cls.id} style={styles.classCard}>
              <Card.Content style={styles.cardContent}>
                <View style={styles.cardHeader}>
                  <View style={styles.classInfo}>
                    <Text variant="titleLarge" style={styles.className}>
                      {cls.name}
                    </Text>
                    <Text variant="bodyMedium" style={styles.classSubject}>
                      {cls.subject}
                    </Text>
                  </View>
                  <View style={styles.actions}>
                    <IconButton
                      icon="pencil"
                      size={20}
                      onPress={() => openDialog(cls)}
                    />
                    <IconButton
                      icon="delete"
                      size={20}
                      onPress={() => handleDelete(cls.id)}
                    />
                  </View>
                </View>

                <View style={styles.classDetails}>
                  <View style={styles.detailRow}>
                    <MaterialCommunityIcons 
                      name="calendar" 
                      size={16} 
                      color={theme.colors.primary} 
                    />
                    <Text variant="bodyMedium" style={styles.detailText}>
                      {cls.schedule}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <MaterialCommunityIcons 
                      name="clock" 
                      size={16} 
                      color={theme.colors.primary} 
                    />
                    <Text variant="bodyMedium" style={styles.detailText}>
                      {cls.time}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <MaterialCommunityIcons 
                      name="map-marker" 
                      size={16} 
                      color={theme.colors.primary} 
                    />
                    <Text variant="bodyMedium" style={styles.detailText}>
                      {cls.room}
                    </Text>
                  </View>
                  
                  <View style={styles.detailRow}>
                    <MaterialCommunityIcons 
                      name="account-group" 
                      size={16} 
                      color={theme.colors.primary} 
                    />
                    <Text variant="bodyMedium" style={styles.detailText}>
                      {cls.students} students
                    </Text>
                  </View>
                </View>
              </Card.Content>
            </Card>
          ))}
        </View>
      </ScrollView>

      {/* Add Class FAB */}
      <FAB
        icon="plus"
        style={[styles.fab, { backgroundColor: theme.colors.primary }]}
        onPress={() => openDialog()}
      />

      {/* Add/Edit Class Dialog */}
      <Portal>
        <Dialog visible={isDialogVisible} onDismiss={closeDialog}>
          <Dialog.Title>
            {editingClass ? 'Edit Class' : 'Add New Class'}
          </Dialog.Title>
          <Dialog.Content>
            <View style={styles.formContainer}>
              <TextInput
                label="Class Name"
                value={formData.name}
                onChangeText={(text) => setFormData({ ...formData, name: text })}
                mode="outlined"
                style={styles.input}
              />
              
              <TextInput
                label="Subject"
                value={formData.subject}
                onChangeText={(text) => setFormData({ ...formData, subject: text })}
                mode="outlined"
                style={styles.input}
              />
              
              <TextInput
                label="Schedule (e.g., Monday, Wednesday, Friday)"
                value={formData.schedule}
                onChangeText={(text) => setFormData({ ...formData, schedule: text })}
                mode="outlined"
                style={styles.input}
              />
              
              <TextInput
                label="Time (e.g., 9:00 AM - 10:30 AM)"
                value={formData.time}
                onChangeText={(text) => setFormData({ ...formData, time: text })}
                mode="outlined"
                style={styles.input}
              />
              
              <TextInput
                label="Room"
                value={formData.room}
                onChangeText={(text) => setFormData({ ...formData, room: text })}
                mode="outlined"
                style={styles.input}
              />
            </View>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={closeDialog}>Cancel</Button>
            <Button onPress={handleSubmit} mode="contained">
              {editingClass ? 'Update' : 'Create'}
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 16,
  },
  title: {
    fontWeight: 'bold',
    marginBottom: 8,
  },
  subtitle: {
    opacity: 0.7,
  },
  classesContainer: {
    padding: 20,
    paddingTop: 0,
    gap: 16,
  },
  classCard: {
    elevation: 2,
  },
  cardContent: {
    padding: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  classInfo: {
    flex: 1,
  },
  className: {
    fontWeight: 'bold',
    marginBottom: 4,
  },
  classSubject: {
    opacity: 0.7,
  },
  actions: {
    flexDirection: 'row',
  },
  classDetails: {
    gap: 8,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    opacity: 0.8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
  formContainer: {
    gap: 16,
  },
  input: {
    backgroundColor: 'transparent',
  },
});
