
import React from 'react';
import { Users, BookOpen, Calendar, TrendingUp, Camera, UserCheck } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import Navbar from '@/components/Navbar';
import AttendanceChart from '@/components/AttendanceChart';

const Dashboard = () => {
  const navigate = useNavigate();
  const teacherName = localStorage.getItem('teacherName') || 'Teacher';

  const stats = [
    {
      title: 'Total Students',
      value: '156',
      icon: Users,
      color: 'bg-ustp-navy',
      change: '+12 this month'
    },
    {
      title: 'Active Classes',
      value: '8',
      icon: BookOpen,
      color: 'bg-ustp-gold',
      change: '3 today'
    },
    {
      title: 'Attendance Rate',
      value: '94.2%',
      icon: TrendingUp,
      color: 'bg-purple-500',
      change: '+2.1% vs last week'
    },
    {
      title: 'Present Today',
      value: '142',
      icon: UserCheck,
      color: 'bg-orange-500',
      change: '91% of total'
    }
  ];

  const recentClasses = [
    { name: 'Computer Science 101', time: '9:00 AM', students: 28, present: 26 },
    { name: 'Data Structures', time: '11:00 AM', students: 24, present: 22 },
    { name: 'Machine Learning', time: '2:00 PM', students: 32, present: 30 },
    { name: 'Web Development', time: '4:00 PM', students: 22, present: 20 }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">
            Welcome back, {teacherName}!
          </h1>
          <p className="text-gray-600 mt-2">
            Here's what's happening with your classes today.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <Card key={index} className="transition-all hover:shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-sm text-green-600 mt-1">{stat.change}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.color}`}>
                    <stat.icon className={`w-6 h-6 ${stat.color === 'bg-ustp-gold' ? 'text-ustp-navy' : 'text-white'}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>
                Common tasks to manage your classes
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                className="w-full justify-start h-14 bg-ustp-navy hover:bg-primary text-ustp-gold hover:text-white"
                onClick={() => navigate('/attendance')}
              >
                <Camera className="mr-3 h-5 w-5" />
                Start Facial Recognition Attendance
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start h-14"
                onClick={() => navigate('/classes')}
              >
                <BookOpen className="mr-3 h-5 w-5" />
                Manage Classes
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start h-14"
                onClick={() => navigate('/students')}
              >
                <Users className="mr-3 h-5 w-5" />
                Manage Students
              </Button>
              <Button 
                variant="outline" 
                className="w-full justify-start h-14"
                onClick={() => navigate('/reports')}
              >
                <TrendingUp className="mr-3 h-5 w-5" />
                View Reports
              </Button>
            </CardContent>
          </Card>

          {/* Today's Classes */}
          <Card>
            <CardHeader>
              <CardTitle>Today's Classes</CardTitle>
              <CardDescription>
                Overview of your scheduled classes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentClasses.map((cls, index) => (
                  <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">{cls.name}</h4>
                      <p className="text-sm text-gray-600">{cls.time}</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">
                        {cls.present}/{cls.students} present
                      </p>
                      <p className="text-xs text-gray-500">
                        {Math.round((cls.present / cls.students) * 100)}% attendance
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Attendance Trends */}
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Attendance Trends</CardTitle>
              <CardDescription>
                Weekly attendance patterns across all classes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <AttendanceChart />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
